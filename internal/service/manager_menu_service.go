package service

import (
	menu "app/internal/dto/adm/manager"
	"app/internal/dto/common"
	"app/internal/model"
	"app/internal/repository"
	"app/pkg/logger"
	"context"
)

// ManagerMenuService 菜单服务
type ManagerMenuService interface {
	// List 分页查询菜单列表
	List(ctx context.Context, req *menu.ManagerMenuListQuery) (*common.PageResponse, error)
	// GetByID 根据ID获取菜单
	GetByID(ctx context.Context, id uint) (*model.ManagerMenu, error)
	// Update 更新菜单
	Update(ctx context.Context, id uint, req *menu.ManagerMenuUpdateQuery) error
}

// ManagerMenuServiceImpl 菜单服务实现
type ManagerMenuServiceImpl struct {
	menuRepo repository.ManagerMenuRepo
	logger   logger.Logger
}

// NewManagerMenuServiceImpl 管理员菜单服务实现并返回实例
func NewManagerMenuServiceImpl(logger logger.Logger, menuRepo repository.ManagerMenuRepo) ManagerMenuService {
	return &ManagerMenuServiceImpl{
		logger:   logger,
		menuRepo: menuRepo,
	}
}

// List 菜单列表分页查询
func (s *ManagerMenuServiceImpl) List(ctx context.Context, req *menu.ManagerMenuListQuery) (*common.PageResponse, error) {
	queryBuilder := repository.NewQueryBuilder()
	queryBuilder.AddCondition("name", req.Name)
	queryBuilder.AddCondition("parent_id", req.ParentID)
	queryBuilder.AddCondition("id", req.ID)
	queryBuilder.AddCondition("type", req.Type)
	queryBuilder.AddCondition("status", req.Status)
	queryBuilder.AddCondition("frame", req.Frame)
	queryBuilder.AddCondition("cache", req.Cache)
	queryBuilder.AddCondition("visible", req.Visible)
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.Field, repository.SortDirection(req.Direction))
	menus, total, err := s.menuRepo.FindListWithCount(ctx, queryBuilder)
	if err != nil {
		return nil, err
	}
	return common.NewPageResponse(total, queryBuilder.Pagination.Page, queryBuilder.Pagination.PageSize, menus), nil
}

// GetByID 根据ID获取菜单
func (s *ManagerMenuServiceImpl) GetByID(ctx context.Context, id uint) (*model.ManagerMenu, error) {
	menu, err := s.menuRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return menu, nil
}

// Update 更新菜单
func (s *ManagerMenuServiceImpl) Update(ctx context.Context, id uint, req *menu.ManagerMenuUpdateQuery) error {
	menu, err := s.menuRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	return s.menuRepo.Update(ctx, menu)
}
