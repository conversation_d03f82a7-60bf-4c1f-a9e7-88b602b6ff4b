// Package service 提供管理员日志的业务逻辑实现
package service

import (
	"app/internal/dto/adm/manager"
	"app/internal/dto/common"
	"app/internal/model"
	"app/internal/repository"
	"app/pkg/logger"
	"context"
)

// ManagerLogService 定义了管理员日志操作的接口
type ManagerLogService interface {
	// List 根据查询条件获取分页的管理员日志列表
	List(ctx context.Context, req *manager.ManagerLogListQuery) (*common.PageResponse, error)

	// Create 创建新的管理员日志记录
	Create(ctx context.Context, req *manager.ManagerLogCreateQuery) error

	// Delete 根据ID删除管理员日志记录
	Delete(ctx context.Context, id uint) error
}

// ManagerLogServiceImpl 实现了 ManagerLogService 接口
type ManagerLogServiceImpl struct {
	logger         logger.Logger
	managerLogRepo repository.ManagerLogRepo
}

// NewManagerLogServiceImpl 创建 ManagerLogServiceImpl 的新实例
func NewManagerLogServiceImpl(logger logger.Logger, managerLogRepo repository.ManagerLogRepo) ManagerLogService {
	return &ManagerLogServiceImpl{
		logger:         logger,
		managerLogRepo: managerLogRepo,
	}
}

// List 根据查询条件获取分页的管理员日志列表
func (s *ManagerLogServiceImpl) List(ctx context.Context, req *manager.ManagerLogListQuery) (*common.PageResponse, error) {
	queryBuilder := repository.NewQueryBuilder()
	queryBuilder.AddCondition("manager_id", req.ManagerID)
	queryBuilder.AddCondition("type", req.Type)
	queryBuilder.AddCondition("module", req.Module)
	queryBuilder.AddCondition("method", req.Method)
	queryBuilder.AddLikeCondition("path", req.Path)
	queryBuilder.AddLikeCondition("params", req.Params)
	queryBuilder.AddLikeCondition("ip", req.IP)
	queryBuilder.AddConditionWithOperator("created_at", repository.OperatorGTE, req.StartTime)
	queryBuilder.AddConditionWithOperator("created_at", repository.OperatorLTE, req.EndTime)
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.Field, repository.SortDirection(req.Direction))

	logs, total, err := s.managerLogRepo.FindListWithCount(ctx, queryBuilder)
	if err != nil {
		return nil, err
	}
	return common.NewPageResponse(total, queryBuilder.Pagination.Page, queryBuilder.Pagination.PageSize, logs), nil
}

// Create 创建新的管理员日志记录
func (s *ManagerLogServiceImpl) Create(ctx context.Context, req *manager.ManagerLogCreateQuery) error {
	return s.managerLogRepo.Create(ctx, &model.ManagerLog{
		ManagerID: uint(req.ManagerID),
		Type:      req.Type,
		Module:    req.Module,
		Content:   req.Content,
		Result:    req.Result,
	})
}

// Delete 根据ID删除管理员日志记录
func (s *ManagerLogServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.managerLogRepo.Delete(ctx, id)
}
