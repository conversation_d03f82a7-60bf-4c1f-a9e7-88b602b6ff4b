package middleware

import (
	"app/internal/config"
	"app/internal/dto/adm/manager"
	"app/internal/model"
	"app/internal/service"
	"app/pkg/jwt"
	"bytes"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ManagerLog 管理员操作日志中间件
func ManagerLog(cfg *config.Config, managerLogService service.ManagerLogService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		startTime := time.Now()

		// 获取请求信息
		path := c.Request.URL.Path
		method := c.Request.Method
		ip := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// 获取请求参数
		var params string
		if method == "GET" {
			params = c.Request.URL.RawQuery
		} else {
			// 读取请求体
			body, err := io.ReadAll(c.Request.Body)
			if err == nil {
				// 恢复请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
				params = string(body)
			}
		}

		// 获取管理员信息
		var managerID uint
		if token := c.GetHeader("Authorization"); token != "" {
			if claims, err := jwt.ParseToken(token, &cfg.JWT); err == nil {
				managerID = claims.UserID
			}
		}

		// 创建响应写入器
		writer := &responseWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime).Milliseconds()

		// 获取操作类型
		operationType := getOperationType(method, path)

		// 获取操作模块
		module := getModuleFromPath(path)

		// 创建日志记录
		log := &manager.ManagerLogCreateQuery{
			ManagerID:  int64(managerID),
			Type:       operationType,
			Module:     module,
			Content:    getOperationContent(method, path),
			Result:     getOperationResult(c.Writer.Status()),
			IP:         ip,
			OperatedAt: time.Now(),
			Method:     method,
			Path:       path,
			Params:     params,
			Duration:   duration,
			UserAgent:  userAgent,
		}

		// 如果响应状态码不是成功，记录错误信息
		if c.Writer.Status() >= 400 {
			log.Error = writer.body.String()
		}

		// 异步记录日志
		go func() {
			if err := managerLogService.Create(c.Request.Context(), log); err != nil {
				// 这里可以添加错误处理逻辑，比如记录到系统日志
			}
		}()
	}
}

// responseWriter 自定义响应写入器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 重写 Write 方法
func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// getOperationType 根据请求方法和路径获取操作类型
func getOperationType(method, path string) int {
	switch method {
	case "GET":
		return model.ManagerLogTypeQuery
	case "POST":
		return model.ManagerLogTypeCreate
	case "PUT", "PATCH":
		return model.ManagerLogTypeUpdate
	case "DELETE":
		return model.ManagerLogTypeDelete
	default:
		return model.ManagerLogTypeOther
	}
}

// getModuleFromPath 从路径中提取模块名称
func getModuleFromPath(path string) string {
	// 移除开头的斜杠
	if len(path) > 0 && path[0] == '/' {
		path = path[1:]
	}

	// 分割路径
	parts := strings.Split(path, "/")
	if len(parts) > 0 {
		return parts[0]
	}
	return "unknown"
}

// getOperationContent 生成操作内容描述
func getOperationContent(method, path string) string {
	module := getModuleFromPath(path)
	switch method {
	case "GET":
		return "查询" + module + "信息"
	case "POST":
		return "创建" + module + "信息"
	case "PUT", "PATCH":
		return "更新" + module + "信息"
	case "DELETE":
		return "删除" + module + "信息"
	default:
		return "其他操作"
	}
}

// getOperationResult 根据状态码获取操作结果
func getOperationResult(statusCode int) int {
	if statusCode >= 200 && statusCode < 300 {
		return model.ManagerLogResultSuccess
	}
	return model.ManagerLogResultFailed
}
