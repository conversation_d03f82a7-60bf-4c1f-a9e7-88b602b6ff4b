package model

import (
	"gorm.io/gorm"
	"time"
)

const (
	ChatsMessagesStatusUnread = 1 // 未读
	ChatsMessagesStatusRead   = 2 // 已读

	ChatsMessagesTypeText  = 1 // 文本
	ChatsMessagesTypeImage = 2 // 图片
	ChatsMessagesTypeFile  = 3 // 文件

	ChatsMessagesSenderTypeUser   = 1 // 用户
	ChatsMessagesSenderTypeAdmin  = 2 // 管理
	ChatsMessagesSenderTypeSystem = 3 // 系统
)

// ChatsMessages 聊天消息
type ChatsMessages struct {
	gorm.Model
	// 操作的管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager    *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	SessionID  string   `gorm:"type:varchar(255);index;comment:会话ID" json:"sessionId"`
	SenderID   uint     `gorm:"type:int unsigned;index;comment:发送者ID" json:"senderId"`
	ReceiverID uint     `gorm:"type:int unsigned;index;comment:接收者ID" json:"receiverId"`
	SenderType int8     `gorm:"type:tinyint;default:1;comment:发送者类型(1:用户,2:管理,3:系统)" json:"senderType"`
	Message    string   `gorm:"type:text;comment:消息" json:"message"`
	Type       int8     `gorm:"type:tinyint;default:1;comment:类型(1:文本,2:图片,3:文件,4:语音,5:视频)" json:"type"`
	Status     int8     `gorm:"type:tinyint;default:1;comment:状态(1:未读,2:已读,3:撤回)" json:"status"`

	// ReadAt 消息被读取的时间 (单聊时为接收者读取时间，群聊时需另设已读状态表)
	ReadAt *time.Time `gorm:"comment:消息被读取的时间" json:"readAt"` // 使用指针支持 NULL

	// ClientMessageID 客户端生成的消息ID，用于幂等性处理和消息去重
	// 在高并发或弱网络环境下，客户端可能会重复发送消息，此ID可帮助后端识别并去重。
	ClientMessageID string `gorm:"type:varchar(100);uniqueIndex;comment:客户端消息ID(用于幂等)" json:"clientMessageId"`
}
