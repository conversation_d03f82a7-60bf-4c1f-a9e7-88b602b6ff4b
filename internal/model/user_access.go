package model

import (
	"database/sql/driver"
	"errors"

	"gorm.io/gorm"

	"github.com/goccy/go-json"
)

const (
	// Access types
	AccessTypeRefresh int8 = 1  // 刷新
	AccessTypeLogin   int8 = 2  // 登录
	AccessTypeLogout  int8 = 3  // 登出
	AccessTypeVisit   int8 = 10 // 访问
)

// Access 前台访问记录表
type Access struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager   *Manager   `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID    uint       `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	Name      string     `gorm:"type:varchar(120) not null;index;comment:名称" json:"name"`
	IP        string     `gorm:"type:varchar(45) not null;index;comment:IP地址" json:"ip"`
	Type      int8       `gorm:"type:tinyint not null;default:1;index;comment:类型(1:刷新,2:登录,3:登出,10:访问)" json:"type"`
	Method    string     `gorm:"type:varchar(10);comment:HTTP方法" json:"method"`
	Route     string     `gorm:"type:varchar(255) not null;index;comment:路由" json:"route"`
	UserAgent string     `gorm:"type:varchar(255);comment:用户代理" json:"userAgent"`
	Referer   string     `gorm:"type:varchar(255);comment:来源页面" json:"referer"`
	Params    string     `gorm:"type:text;comment:查询参数" json:"params"`
	Duration  int64      `gorm:"comment:请求处理耗时 (毫秒)" json:"duration_ms,omitempty"`
	Location  string     `gorm:"type:varchar(255);comment:操作地点" json:"location"`
	Data      AccessData `gorm:"type:json;comment:数据" json:"data"`
}

// AccessData 访问记录数据
type AccessData struct {
	Headers string `json:"headers"` // 头信息
}

// Value implements the driver.Valuer interface
func (d AccessData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *AccessData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}
