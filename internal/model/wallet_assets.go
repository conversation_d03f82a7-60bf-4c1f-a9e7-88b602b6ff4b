package model

import (
	"app/pkg/logger"
	"database/sql/driver"
	"errors"
	"fmt"

	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

const (
	WalletAssetsTypeDigitalCurrency int8 = 1 // 数字货币

	WalletAssetsStatusDisabled int8 = -1 // 禁用
	WalletAssetsStatusEnabled  int8 = 10 // 启用
)

// WalletAssets 钱包资产管理
type WalletAssets struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager  *Manager         `gorm:"foreignKey:ManagerID;index:idx_admin_status,priority:1" json:"manager,omitempty"`
	Name     string           `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Currency string           `gorm:"type:varchar(10) not null;uniqueIndex;comment:货币符号" json:"currency"`
	Icon     string           `gorm:"type:varchar(255) not null;comment:图标" json:"icon"`
	Type     int8             `gorm:"type:tinyint not null;default:1;index;comment:类型(1:数字货币)" json:"type"`
	Rate     int64            `gorm:"type:bigint not null;default:10000;comment:汇率(基准值10000)" json:"rate"`
	Status   int8             `gorm:"type:tinyint not null;default:10;index:idx_admin_status,priority:2;comment:状态(-1:禁用,10:启用)" json:"status"`
	Decimals uint8            `gorm:"type:tinyint unsigned not null;default:2;comment:小数位数" json:"decimals"`
	Network  string           `gorm:"type:varchar(50);comment:网络" json:"network"`
	Desc     string           `gorm:"type:text;comment:描述" json:"desc"`
	Data     WalletAssetsData `gorm:"type:json;comment:数据" json:"data"`
}

// WalletAssetsData 钱包资产数据
type WalletAssetsData struct {
	ContractAddress string         `json:"contractAddress,omitempty"` // 合约地址
	ChainID         int64          `json:"chainId,omitempty"`         // 链ID
	ExplorerURL     string         `json:"explorerUrl,omitempty"`     // 区块浏览器URL
	WithdrawFee     int64          `json:"withdrawFee,omitempty"`     // 提现手续费
	MinWithdraw     int64          `json:"minWithdraw,omitempty"`     // 最小提现金额
	MaxWithdraw     int64          `json:"maxWithdraw,omitempty"`     // 最大提现金额
	IsNative        bool           `json:"isNative,omitempty"`        // 是否为原生代币
	ExtraInfo       map[string]any `json:"extraInfo,omitempty"`       // 额外信息
}

// Value implements the driver.Valuer interface
func (d WalletAssetsData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *WalletAssetsData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// IsEnabled 检查资产是否启用
func (w *WalletAssets) IsEnabled() bool {
	return w.Status == WalletAssetsStatusEnabled
}

// IsDisabled 检查资产是否禁用
func (w *WalletAssets) IsDisabled() bool {
	return w.Status == WalletAssetsStatusDisabled
}

// IsDigitalCurrency 检查是否是数字货币
func (w *WalletAssets) IsDigitalCurrency() bool {
	return w.Type == WalletAssetsTypeDigitalCurrency
}

// Enable 启用资产
func (w *WalletAssets) Enable() {
	w.Status = WalletAssetsStatusEnabled
}

// Disable 禁用资产
func (w *WalletAssets) Disable() {
	w.Status = WalletAssetsStatusDisabled
}

// GetRateFloat 获取浮点数形式的汇率
func (w *WalletAssets) GetRateFloat() float64 {
	return float64(w.Rate) / 10000.0
}

// SetRateFloat 设置浮点数形式的汇率
func (w *WalletAssets) SetRateFloat(rate float64) {
	w.Rate = int64(rate * 10000)
}

// Validate 验证资产数据
func (w *WalletAssets) Validate() error {
	if w.Name == "" {
		return errors.New("name is required")
	}
	if len(w.Name) > 60 {
		return errors.New("name length must be less than 60")
	}
	if w.Currency == "" {
		return errors.New("currency is required")
	}
	if len(w.Currency) > 10 {
		return errors.New("currency length must be less than 10")
	}
	if w.Status != WalletAssetsStatusDisabled && w.Status != WalletAssetsStatusEnabled {
		return errors.New("invalid status")
	}
	if w.Type != WalletAssetsTypeDigitalCurrency {
		return errors.New("invalid type")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (w *WalletAssets) BeforeCreate(tx *gorm.DB) error {
	return w.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (w *WalletAssets) BeforeUpdate(tx *gorm.DB) error {
	return w.Validate()
}

// InitWalletAssets 初始化默认钱包资产
func InitWalletAssets(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 WalletAssets 表
	if err := db.AutoMigrate(&WalletAssets{}); err != nil {
		return fmt.Errorf("failed to auto migrate WalletAssets table in init: %v", err)
	}

	// 检查表是否为空
	var count int64
	result := db.Model(&WalletAssets{}).Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check WalletAssets count in init: %v", result.Error)
	}

	// 如果表为空，添加初始数据
	if count == 0 {
		assets := []*WalletAssets{}

		if err := db.CreateInBatches(assets, len(assets)).Error; err != nil {
			return fmt.Errorf("failed to create initial wallet assets in init: %v", err)
		}

		appLogger.Info("Initial wallet assets created successfully.")
	}

	return nil
}
