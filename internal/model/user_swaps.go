package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	// SwapsTypeAssets 资产交换类型
	SwapsTypeAssets int8 = 1

	// SwapsStatusRejected 资产交换状态：拒绝
	SwapsStatusRejected int8 = -1
	// SwapsStatusPending 资产交换状态：处理中
	SwapsStatusPending int8 = 10
	// SwapsStatusCompleted 资产交换状态：完成
	SwapsStatusCompleted int8 = 20
)

// Swaps 资产交换
type Swaps struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager         *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID          uint     `gorm:"type:int unsigned;index;comment:用户ID" json:"userId"`
	SendAssetsID    uint     `gorm:"type:int unsigned;index;comment:发送资产ID" json:"sendAssetsId"`
	ReceiveAssetsID uint     `gorm:"type:int unsigned;index;comment:接收资产ID" json:"receiveAssetsId"`
	SendAmount      float64  `gorm:"type:decimal(16,4);comment:发送金额" json:"sendAmount"`
	ReceiveAmount   float64  `gorm:"type:decimal(16,4);comment:接收金额" json:"receiveAmount"`
	Rate            float64  `gorm:"type:decimal(16,4);comment:汇率" json:"rate"`
	Fee             float64  `gorm:"type:decimal(16,4) not null;default:0;comment:手续费" json:"fee"`
	Type            int8     `gorm:"type:tinyint;default:1;index;comment:类型(1:资产)" json:"type"`
	Status          int8     `gorm:"type:tinyint;default:20;index;comment:状态(-1:已拒绝,10:待处理,20:已完成)" json:"status"`
}

// IsAssetsSwaps 检查是否为资产交换类型
func (s *Swaps) IsAssetsSwaps() bool {
	return s.Type == SwapsTypeAssets
}

// IsRejected 检查是否为拒绝状态
func (s *Swaps) IsRejected() bool {
	return s.Status == SwapsStatusRejected
}

// IsPending 检查是否为待处理状态
func (s *Swaps) IsPending() bool {
	return s.Status == SwapsStatusPending
}

// IsCompleted 检查是否为已完成状态
func (s *Swaps) IsCompleted() bool {
	return s.Status == SwapsStatusCompleted
}

// Validate 校验 Swaps 数据
func (s *Swaps) Validate() error {
	if s.UserID == 0 {
		return errors.New("用户ID不能为空")
	}
	if s.SendAssetsID == 0 {
		return errors.New("发送资产ID不能为空")
	}
	if s.ReceiveAssetsID == 0 {
		return errors.New("接收资产ID不能为空")
	}
	if s.SendAmount <= 0 {
		return errors.New("发送金额必须大于0")
	}
	if s.ReceiveAmount <= 0 {
		return errors.New("接收金额必须大于0")
	}
	if s.Rate <= 0 {
		return errors.New("汇率必须大于0")
	}
	// 可以在这里添加更多关于 Type 和 Status 的校验
	return nil
}

// BeforeCreate GORM hook for data validation and pre-processing
func (s *Swaps) BeforeCreate(tx *gorm.DB) (err error) {
	return s.Validate()
}

// BeforeUpdate GORM hook for data validation
func (s *Swaps) BeforeUpdate(tx *gorm.DB) (err error) {
	return s.Validate()
}
