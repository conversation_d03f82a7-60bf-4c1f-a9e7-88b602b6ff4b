package model

import (
	"app/pkg/logger"
	"database/sql/driver"
	"errors"
	"fmt"

	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

const (
	// 产品翻译键名
	ProductTranslateName = "productName" // 产品名称翻译键
	ProductTranslateDesc = "productDesc" // 产品描述翻译键

	// 产品类型
	ProductTypeDefault int8 = 1 // 默认类型

	// 产品状态
	ProductStatusDisabled int8 = -1 // 禁用
	ProductStatusEnabled  int8 = 10 // 启用

	// 推荐状态
	ProductRecommendedYes int8 = 1 // 推荐
	ProductRecommendedNo  int8 = 2 // 不推荐

	// 翻译状态
	ProductTranslateYes int8 = 1 // 已翻译
	ProductTranslateNo  int8 = 2 // 未翻译
)

// Product 产品表
type Product struct {
	gorm.Model
	MerchantID  uint        `gorm:"type:int unsigned not null;index;comment:商户ID" json:"merchantId"`
	CategoryID  uint        `gorm:"type:int unsigned not null;index;comment:类目ID" json:"categoryId"`
	AssetsID    uint        `gorm:"type:int unsigned not null;index;comment:资产ID" json:"assetsId"`
	Name        string      `gorm:"type:varchar(64) not null;index;comment:产品名称" json:"name"`
	Symbol      string      `gorm:"type:varchar(64) not null;index;comment:产品标识" json:"symbol"`
	Images      string      `gorm:"type:text;comment:产品图片" json:"images"`
	Money       float64     `gorm:"type:decimal(16,2) not null;comment:金额" json:"money"`
	Type        int8        `gorm:"type:tinyint not null;default:1;index;comment:类型(1:默认)" json:"type"`
	Sort        int16       `gorm:"type:smallint not null;default:99;index;comment:排序" json:"sort"`
	IsTranslate int8        `gorm:"type:tinyint not null;default:2;index;comment:是否翻译(1:是,2:否)" json:"isTranslate"`
	Status      int8        `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Desc        string      `gorm:"type:text;comment:产品描述" json:"desc"`
	Data        ProductData `gorm:"type:json;comment:配置" json:"data"`
}

// ProductData 产品数据
type ProductData struct{}

// Value implements the driver.Valuer interface
func (d ProductData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *ProductData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// IsEnabled 检查产品是否启用
func (p *Product) IsEnabled() bool {
	return p.Status == ProductStatusEnabled
}

// IsDisabled 检查产品是否禁用
func (p *Product) IsDisabled() bool {
	return p.Status == ProductStatusDisabled
}

// IsDefaultType 检查是否是默认类型
func (p *Product) IsDefaultType() bool {
	return p.Type == ProductTypeDefault
}

// IsTranslated 检查是否已翻译
func (p *Product) IsTranslated() bool {
	return p.IsTranslate == ProductTranslateYes
}

// Validate 验证产品数据
func (p *Product) Validate() error {
	if p.Symbol == "" {
		return errors.New("symbol is required")
	}
	if len(p.Symbol) > 64 {
		return errors.New("symbol length must be less than 64")
	}
	if p.Name == "" {
		return errors.New("name is required")
	}
	if len(p.Name) > 64 {
		return errors.New("name length must be less than 64")
	}
	if p.Status != ProductStatusDisabled && p.Status != ProductStatusEnabled {
		return errors.New("invalid status")
	}
	if p.Type != ProductTypeDefault {
		return errors.New("invalid type")
	}
	if p.IsTranslate != ProductTranslateYes && p.IsTranslate != ProductTranslateNo {
		return errors.New("invalid translate value")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (p *Product) BeforeCreate(tx *gorm.DB) error {
	return p.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (p *Product) BeforeUpdate(tx *gorm.DB) error {
	return p.Validate()
}

// InitProductProducts 初始化产品
func InitProductProducts(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 Product 表
	if err := db.AutoMigrate(&Product{}); err != nil {
		return fmt.Errorf("failed to auto migrate Product table in init: %v", err)
	}

	// 检查表是否为空
	var count int64
	result := db.Model(&Product{}).Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check Product count in init: %v", result.Error)
	}

	// 如果表为空，添加初始数据
	if count == 0 {
		products := initProducts()

		if err := db.CreateInBatches(products, len(products)).Error; err != nil {
			return fmt.Errorf("failed to create initial products in init: %v", err)
		}

		appLogger.Info("Initial products created successfully.")
	}

	return nil
}

// initProducts 返回初始产品数据
func initProducts() []*Product {
	return []*Product{}
}
