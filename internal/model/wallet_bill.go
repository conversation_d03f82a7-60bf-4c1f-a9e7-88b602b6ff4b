package model

import (
	"gorm.io/gorm"
)

// WalletBillTypePrefix 钱包账单类型前缀
const WalletBillTypePrefix = "walletBillType"

const (
	// 信用（传入）账单类型
	BillTypeDeposit            int8 = 1  // 充值
	BillTypeSystemReward       int8 = 2  // 系统奖励
	BillTypeRegisterReward     int8 = 3  // 注册奖励
	BillTypeInviteReward       int8 = 4  // 邀请奖励
	BillTypeDistributionReward int8 = 5  // 分销奖励
	BillTypeBalanceUnfreeze    int8 = 6  // 余额解冻
	BillTypeSystemAddition     int8 = 7  // 系统加款
	BillTypeWithdrawalReject   int8 = 8  // 提现拒绝
	BillTypeProductRefund      int8 = 9  // 产品退款
	BillTypeProductEarnings    int8 = 10 // 产品收益
	BillTypeTransferReceive    int8 = 11 // 转账接收
	BillTypeSwapsReceive       int8 = 12 // 闪兑接收

	// 借记（支出）账单类型
	BillTypeWithdrawal         int8 = -1 // 提现
	BillTypeSystemDeduction    int8 = -2 // 系统扣款
	BillTypeProductPurchase    int8 = -3 // 购买产品
	BillTypeMembershipPurchase int8 = -4 // 购买会员
	BillTypeBalanceFreeze      int8 = -5 // 余额冻结
	BillTypeTransferSend       int8 = -6 // 转账发送
	BillTypeSwapsSend          int8 = -7 // 闪兑发送
)

// WalletBill 钱包账单
type WalletBill struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager       *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID        uint     `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	AssetsID      uint     `gorm:"type:int unsigned not null;index;comment:资产ID" json:"assetsId"`
	SourceID      uint     `gorm:"type:int unsigned not null;index;comment:来源ID" json:"sourceId"`
	Type          int8     `gorm:"type:tinyint not null;index;comment:类型" json:"type"`
	Name          string   `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Amount        float64  `gorm:"type:decimal(24,8);not null;comment:交易金额 (正数表示增加,负数表示减少)" json:"amount"`
	BeforeBalance float64  `gorm:"type:decimal(24,8);not null;comment:交易前余额|资产" json:"before_balance"`
	AfterBalance  float64  `gorm:"type:decimal(24,8);not null;comment:交易后余额|资产" json:"after_balance"`
	Fee           float64  `gorm:"type:decimal(24,8);default:0;comment:手续费" json:"fee,omitempty"`
	Desc          string   `gorm:"type:varchar(255);comment:描述" json:"desc"`
}
