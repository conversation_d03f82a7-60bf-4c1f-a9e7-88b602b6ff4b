package model

import (
	"app/pkg/logger"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

const (
	// 操作类型
	ManagerLogTypeLogin     int = 1  // 登录
	ManagerLogTypeLogout    int = 2  // 登出
	ManagerLogTypeCreate    int = 3  // 创建
	ManagerLogTypeUpdate    int = 4  // 更新
	ManagerLogTypeDelete    int = 5  // 删除
	ManagerLogTypeQuery     int = 6  // 普通查询
	ManagerLogTypeSensitive int = 7  // 敏感查询
	ManagerLogTypeOther     int = 99 // 其他

	// 操作结果
	ManagerLogResultSuccess int = 1 // 成功
	ManagerLogResultFailed  int = 2 // 失败

	// 敏感模块列表
	SensitiveModules = "user,finance,payment,security,audit"
)

// ManagerLog 管理员操作日志
type ManagerLog struct {
	BaseModel

	// 操作的管理员ID
	ManagerID uint `gorm:"index;not null;comment:'操作管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	// 操作类型
	Type int `gorm:"type:smallint;not null;comment:'操作类型'" json:"type"`
	// 操作模块 (例如: user, order, product 等)
	Module string `gorm:"type:varchar(50);not null;comment:'操作模块'" json:"module"`
	// 操作内容
	Content string `gorm:"type:text;not null;comment:'操作内容'" json:"content"`
	// 操作结果
	Result int `gorm:"type:smallint;not null;comment:'操作结果'" json:"result"`
	// 错误信息（如果操作失败）
	Error string `gorm:"type:text;comment:'错误信息'" json:"error,omitempty"`
	// 操作IP
	IP string `gorm:"type:varchar(45);comment:'操作IP'" json:"ip"`
	// 操作时间
	OperatedAt time.Time `gorm:"not null;comment:'操作时间'" json:"operated_at"`
	// 请求方法 (GET, POST, PUT, DELETE 等)
	Method string `gorm:"type:varchar(10);comment:'请求方法'" json:"method"`
	// 请求路径
	Path string `gorm:"type:varchar(255);comment:'请求路径'" json:"path"`
	// 请求参数
	Params string `gorm:"type:text;comment:'请求参数'" json:"params,omitempty"`
	// 响应数据
	Response string `gorm:"type:text;comment:'响应数据'" json:"response,omitempty"`
	// 操作耗时（毫秒）
	Duration int64 `gorm:"type:bigint;comment:'操作耗时(ms)'" json:"duration"`
	// 用户代理
	UserAgent string `gorm:"type:varchar(255);comment:'用户代理'" json:"user_agent,omitempty"`
}

// IsSuccess 检查操作是否成功
func (l *ManagerLog) IsSuccess() bool {
	return l.Result == ManagerLogResultSuccess
}

// IsFailed 检查操作是否失败
func (l *ManagerLog) IsFailed() bool {
	return l.Result == ManagerLogResultFailed
}

// IsLogin 检查是否是登录操作
func (l *ManagerLog) IsLogin() bool {
	return l.Type == ManagerLogTypeLogin
}

// IsLogout 检查是否是登出操作
func (l *ManagerLog) IsLogout() bool {
	return l.Type == ManagerLogTypeLogout
}

// IsCreate 检查是否是创建操作
func (l *ManagerLog) IsCreate() bool {
	return l.Type == ManagerLogTypeCreate
}

// IsUpdate 检查是否是更新操作
func (l *ManagerLog) IsUpdate() bool {
	return l.Type == ManagerLogTypeUpdate
}

// IsDelete 检查是否是删除操作
func (l *ManagerLog) IsDelete() bool {
	return l.Type == ManagerLogTypeDelete
}

// IsQuery 检查是否是普通查询操作
func (l *ManagerLog) IsQuery() bool {
	return l.Type == ManagerLogTypeQuery
}

// IsSensitive 检查是否是敏感查询操作
func (l *ManagerLog) IsSensitive() bool {
	return l.Type == ManagerLogTypeSensitive
}

// IsSensitiveModule 检查是否是敏感模块
func (l *ManagerLog) IsSensitiveModule() bool {
	return strings.Contains(SensitiveModules, l.Module)
}

// Validate 验证日志数据
func (l *ManagerLog) Validate() error {
	if l.ManagerID == 0 {
		return errors.New("manager_id is required")
	}
	if l.Type < ManagerLogTypeLogin || l.Type > ManagerLogTypeOther {
		return errors.New("invalid type")
	}
	if l.Module == "" {
		return errors.New("module is required")
	}
	if l.Content == "" {
		return errors.New("content is required")
	}
	if l.Result < ManagerLogResultSuccess || l.Result > ManagerLogResultFailed {
		return errors.New("invalid result")
	}
	if l.OperatedAt.IsZero() {
		return errors.New("operated_at is required")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (l *ManagerLog) BeforeCreate(tx *gorm.DB) error {
	return l.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (l *ManagerLog) BeforeUpdate(tx *gorm.DB) error {
	return l.Validate()
}

// SetSuccess 设置为成功
func (l *ManagerLog) SetSuccess() {
	l.Result = ManagerLogResultSuccess
}

// SetFailed 设置为失败
func (l *ManagerLog) SetFailed(err error) {
	l.Result = ManagerLogResultFailed
	if err != nil {
		l.Error = err.Error()
	}
}

// GetManager 获取关联的管理员
func (l *ManagerLog) GetManager() *Manager {
	return l.Manager
}

// SetManager 设置关联的管理员
func (l *ManagerLog) SetManager(manager *Manager) {
	l.Manager = manager
	l.ManagerID = manager.ID
}

// ShouldLogQuery 判断是否需要记录查询操作
func (l *ManagerLog) ShouldLogQuery() bool {
	// 如果是敏感模块的查询，需要记录
	if l.IsSensitiveModule() {
		l.Type = ManagerLogTypeSensitive
		return true
	}
	// 普通查询不记录
	return false
}

// InitManagerLog 初始化管理日志
func InitManagerLog(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 ManagerLog 表
	if err := db.AutoMigrate(&ManagerLog{}); err != nil {
		return fmt.Errorf("failed to auto migrate ManagerLog table in init: %v", err)
	}

	appLogger.Info("ManagerLog table initialized successfully.")
	return nil
}
