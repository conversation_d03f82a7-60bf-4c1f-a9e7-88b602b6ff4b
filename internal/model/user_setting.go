package model

import (
	"gorm.io/gorm"
)

// Setting 用户设置
type Setting struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID  uint     `gorm:"type:int unsigned not null;uniqueIndex:idx_user_field;index;comment:用户ID" json:"userId"`
	Name    string   `gorm:"type:varchar(50) not null;comment:名称" json:"name"`
	Type    string   `gorm:"type:varchar(50) not null;comment:类型" json:"type"`
	Field   string   `gorm:"type:varchar(50) not null;uniqueIndex:idx_user_field;comment:字段名" json:"field"`
	Value   string   `gorm:"type:text;comment:字段值" json:"value"`
}
