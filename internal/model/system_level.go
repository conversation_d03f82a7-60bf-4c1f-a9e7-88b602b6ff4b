package model

import (
	"app/pkg/logger"
	"database/sql/driver"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

const (
	// 等级状态
	LevelStatusDisabled int8 = -1 // 禁用
	LevelStatusEnabled  int8 = 10 // 启用

	// 等级类型
	LevelTypeMember int8 = 1 // 会员等级
)

// Level 系统等级配置
type Level struct {
	gorm.Model
	Name   string    `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Icon   string    `gorm:"type:varchar(255);comment:图标" json:"icon,omitempty"`
	Symbol string    `gorm:"type:varchar(20) not null;uniqueIndex;comment:等级标识" json:"symbol"`
	Type   int8      `gorm:"type:tinyint not null;default:1;index:idx_type_status,priority:1;comment:类型(1:会员等级)" json:"type"`
	Money  int64     `gorm:"type:bigint not null;comment:金额 - 升级所需金额" json:"money"`
	Days   int       `gorm:"type:smallint not null;comment:天数 - 等级有效期天数" json:"days"`
	Status int8      `gorm:"type:tinyint not null;default:10;index:idx_type_status,priority:2;comment:状态(-1:禁用,10:启用)" json:"status"`
	Desc   string    `gorm:"type:text;comment:详情" json:"desc,omitempty"`
	Data   LevelData `gorm:"type:json;comment:数据" json:"data"`
}

// LevelData 系统等级数据
type LevelData struct {
	Discount        int64    `json:"discount,omitempty"`        // 折扣率(基准值10000，如9500表示95%)
	CashbackRate    int64    `json:"cashbackRate,omitempty"`    // 返现比例(基准值10000)
	WithdrawLimit   int64    `json:"withdrawLimit,omitempty"`   // 提现限额
	DailyBonus      int64    `json:"dailyBonus,omitempty"`      // 每日奖励
	ReferralBonus   int64    `json:"referralBonus,omitempty"`   // 推荐奖励
	MaxReferrals    int      `json:"maxReferrals,omitempty"`    // 最大推荐人数
	SpecialFeatures []string `json:"specialFeatures,omitempty"` // 特殊功能列表
	BadgeColor      string   `json:"badgeColor,omitempty"`      // 徽章颜色
	Priority        int      `json:"priority,omitempty"`        // 优先级
}

// Value implements the driver.Valuer interface
func (d LevelData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *LevelData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// TableName 指定表名
func (Level) TableName() string {
	return "system_levels"
}

// IsEnabled 检查等级是否启用
func (l *Level) IsEnabled() bool {
	return l.Status == LevelStatusEnabled
}

// IsDisabled 检查等级是否禁用
func (l *Level) IsDisabled() bool {
	return l.Status == LevelStatusDisabled
}

// IsMemberType 检查是否是会员等级
func (l *Level) IsMemberType() bool {
	return l.Type == LevelTypeMember
}

// Enable 启用等级
func (l *Level) Enable() {
	l.Status = LevelStatusEnabled
}

// Disable 禁用等级
func (l *Level) Disable() {
	l.Status = LevelStatusDisabled
}

// GetMoneyFloat 获取浮点数形式的金额
func (l *Level) GetMoneyFloat() float64 {
	return float64(l.Money) / 100.0
}

// SetMoneyFloat 设置浮点数形式的金额
func (l *Level) SetMoneyFloat(money float64) {
	l.Money = int64(money * 100)
}

// GetDiscountFloat 获取浮点数形式的折扣率
func (l *Level) GetDiscountFloat() float64 {
	return float64(l.Data.Discount) / 10000.0
}

// SetDiscountFloat 设置浮点数形式的折扣率
func (l *Level) SetDiscountFloat(discount float64) {
	l.Data.Discount = int64(discount * 10000)
}

// GetCashbackRateFloat 获取浮点数形式的返现比例
func (l *Level) GetCashbackRateFloat() float64 {
	return float64(l.Data.CashbackRate) / 10000.0
}

// SetCashbackRateFloat 设置浮点数形式的返现比例
func (l *Level) SetCashbackRateFloat(rate float64) {
	l.Data.CashbackRate = int64(rate * 10000)
}

// StatusText 获取等级状态的文本描述
func (l *Level) StatusText() string {
	switch l.Status {
	case LevelStatusDisabled:
		return "已禁用"
	case LevelStatusEnabled:
		return "已启用"
	default:
		return fmt.Sprintf("未知状态(%d)", l.Status)
	}
}

// Validate 验证等级数据
func (l *Level) Validate() error {
	if l.Name == "" {
		return errors.New("name is required")
	}
	if len(l.Name) > 60 {
		return errors.New("name length must be less than 60")
	}
	if l.Symbol == "" {
		return errors.New("symbol is required")
	}
	if len(l.Symbol) > 20 {
		return errors.New("symbol length must be less than 20")
	}

	// 验证Symbol格式
	if err := l.validateSymbolFormat(); err != nil {
		return err
	}

	if l.Days < 0 {
		return errors.New("days cannot be negative")
	}
	if l.Money < 0 {
		return errors.New("money cannot be negative")
	}

	// 验证状态
	switch l.Status {
	case LevelStatusDisabled, LevelStatusEnabled:
		// 有效状态
	default:
		return fmt.Errorf("invalid level status: %d", l.Status)
	}

	// 验证LevelData
	if err := l.validateLevelData(); err != nil {
		return err
	}

	return nil
}

// validateSymbolFormat 验证Symbol格式
func (l *Level) validateSymbolFormat() error {
	// Symbol应该是大写字母、数字和下划线的组合
	pattern := `^[A-Z0-9_]+$`
	matched, err := regexp.MatchString(pattern, l.Symbol)
	if err != nil {
		return fmt.Errorf("symbol format validation error: %v", err)
	}
	if !matched {
		return errors.New("symbol must contain only uppercase letters, numbers and underscores")
	}

	// 检查Symbol是否以合理的前缀开始
	validPrefixes := []string{"LV", "LEVEL", "VIP", "MEMBER"}
	for _, prefix := range validPrefixes {
		if strings.HasPrefix(l.Symbol, prefix) {
			return nil
		}
	}

	return errors.New("symbol should start with a valid prefix (LV, LEVEL, VIP, MEMBER)")
}

// validateLevelData 验证LevelData
func (l *Level) validateLevelData() error {
	if l.Data.Discount < 0 || l.Data.Discount > 10000 {
		return errors.New("discount must be between 0 and 10000")
	}
	if l.Data.CashbackRate < 0 || l.Data.CashbackRate > 10000 {
		return errors.New("cashback rate must be between 0 and 10000")
	}
	if l.Data.WithdrawLimit < 0 {
		return errors.New("withdraw limit cannot be negative")
	}
	if l.Data.DailyBonus < 0 {
		return errors.New("daily bonus cannot be negative")
	}
	if l.Data.ReferralBonus < 0 {
		return errors.New("referral bonus cannot be negative")
	}
	if l.Data.MaxReferrals < 0 {
		return errors.New("max referrals cannot be negative")
	}
	return nil
}

// BeforeCreate GORM hook: 创建前验证
func (l *Level) BeforeCreate(tx *gorm.DB) (err error) {
	return l.Validate()
}

// BeforeUpdate GORM hook: 更新前验证
func (l *Level) BeforeUpdate(tx *gorm.DB) (err error) {
	return l.Validate()
}

// InitSystemLevels 初始化系统等级
func InitSystemLevels(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 Level 表
	if err := db.AutoMigrate(&Level{}); err != nil {
		return fmt.Errorf("failed to auto migrate Level table in init: %v", err)
	}

	// 检查表是否为空
	var count int64
	result := db.Model(&Level{}).Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check Level count in init: %v", result.Error)
	}

	// 如果表为空，添加初始数据
	if count == 0 {
		levels := []*Level{}

		if err := db.CreateInBatches(levels, len(levels)).Error; err != nil {
			return fmt.Errorf("failed to create initial levels in init: %v", err)
		}

		appLogger.Info("Initial system levels created successfully.")
	}

	return nil
}
