package models

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"zfeng/core/model"
	"zfeng/core/views"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	// LevelNameTranslatePrefix 等级名称翻译前缀
	LevelNameTranslatePrefix = "levelName%v"
	// LevelDescTranslatePrefix 等级详情翻译前缀
	LevelDescTranslatePrefix = "levelDesc%v"

	LevelStatusDisabled int8 = -1 // 禁用
	LevelStatusEnabled  int8 = 10 // 启用

	LevelTypeMember int8 = 1 // 会员等级
)

// Level 系统等级配置
type Level struct {
	model.BaseModel
	AdminID uint      `gorm:"type:int unsigned not null;uniqueIndex:idx_admin_symbol;comment:管理ID" json:"adminId"`
	Name    string    `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	Icon    string    `gorm:"type:varchar(255);comment:图标" json:"icon"`
	Symbol  int8      `gorm:"type:tinyint not null;uniqueIndex:idx_admin_symbol;comment:标识" json:"symbol"`
	Type    int8      `gorm:"type:tinyint not null;default:1;index;comment:类型(1:等级)" json:"type"`
	Money   float64   `gorm:"type:decimal(12,2) not null;comment:金额" json:"money"`
	Days    int       `gorm:"type:smallint not null;comment:天数" json:"days"`
	Status  int8      `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Desc    string    `gorm:"type:text;comment:详情" json:"desc"`
	Data    LevelData `gorm:"type:json;comment:数据" json:"data"`
}

// LevelData 系统等级数据
type LevelData struct {
	Discount float64               `json:"discount"` // 折扣率
	Benefits []*views.SelectOption `json:"benefits"` // 等级权益
}

// Value implements the driver.Valuer interface
func (d LevelData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *LevelData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Level{}); err != nil {
		panic("Failed to auto migrate Level table: " + err.Error())
	}

	// Initialize system levels
	if err := InitSystemLevels(db.DB); err != nil {
		panic("Failed to initialize system levels: " + err.Error())
	}
}

// InitSystemLevels initializes the default system levels
func InitSystemLevels(db *gorm.DB) error {
	var count int64
	if err := db.Model(&Level{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		levels := initSystemLevels()
		for _, v := range levels {
			v.Name = fmt.Sprintf(LevelNameTranslatePrefix, v.Symbol)
			v.Desc = fmt.Sprintf(LevelDescTranslatePrefix, v.Symbol)
		}
		return db.CreateInBatches(levels, len(levels)).Error
	}

	return nil
}

func initSystemLevels() []*Level {
	levels := []*Level{
		{
			AdminID: SuperAdminID,
			Symbol:  1,
			Name:    "普通会员",
			Desc: `<div>
				<ul>
					<li>享受95折优惠</li>
					<li>基础客服支持</li>
					<li>基础提现额度</li>
				</ul>
			</div>`,
			Icon:  "/icons/level1.png",
			Money: 100,
			Days:  365,
			Data: LevelData{
				Discount: 0.95, // 5% 折扣
				Benefits: []*views.SelectOption{},
			},
		},
		{
			AdminID: SuperAdminID,
			Symbol:  2,
			Name:    "黄金会员",
			Desc: `<div>
				<ul>
					<li>享受90折优惠</li>
					<li>优先客服支持</li>
					<li>提升提现额度</li>
					<li>专属活动参与资格</li>
					<li>每月赠送积分</li>
				</ul>
			</div>`,
			Icon:  "/icons/level2.png",
			Money: 500,
			Days:  365,
			Data: LevelData{
				Discount: 0.90, // 10% 折扣
				Benefits: []*views.SelectOption{},
			},
		},
		{
			AdminID: SuperAdminID,
			Symbol:  3,
			Name:    "钻石会员",
			Desc: `<div>
				<ul>
					<li>享受85折优惠</li>
					<li>24小时专属客服</li>
					<li>更高提现额度</li>
					<li>专属活动优先参与</li>
					<li>每月双倍积分</li>
					<li>生日特别礼遇</li>
					<li>专属理财顾问</li>
				</ul>
			</div>`,
			Icon:  "/icons/level3.png",
			Money: 2000,
			Days:  365,
			Data: LevelData{
				Discount: 0.85, // 15% 折扣
				Benefits: []*views.SelectOption{},
			},
		},
		{
			AdminID: SuperAdminID,
			Symbol:  4,
			Name:    "至尊会员",
			Desc: `<div>
				<ul>
					<li>享受80折优惠</li>
					<li>24小时一对一专属服务</li>
					<li>极速提现通道</li>
					<li>专属活动VIP席位</li>
					<li>每月三倍积分</li>
					<li>全年重要节日礼遇</li>
					<li>高级理财顾问团队</li>
					<li>专属投资机会</li>
					<li>机场VIP通道</li>
				</ul>
			</div>`,
			Icon:  "/icons/level4.png",
			Money: 5000,
			Days:  365,
			Data: LevelData{
				Discount: 0.80, // 20% 折扣
				Benefits: []*views.SelectOption{},
			},
		},
		{
			AdminID: SuperAdminID,
			Symbol:  5,
			Name:    "超级会员",
			Desc: `<div>
				<ul>
					<li>享受75折最优惠</li>
					<li>24小时私人管家服务</li>
					<li>无上限提现特权</li>
					<li>专属活动最高优先级</li>
					<li>积分五倍累积</li>
					<li>全年贵宾礼遇服务</li>
					<li>私人定制理财方案</li>
					<li>优先投资机会</li>
					<li>全球机场VIP通道</li>
					<li>专属医疗绿色通道</li>
					<li>高端休闲会所使用权</li>
				</ul>
			</div>`,
			Icon:  "/icons/level5.png",
			Money: 10000,
			Days:  365,
			Data: LevelData{
				Discount: 0.75, // 25% 折扣
				Benefits: []*views.SelectOption{},
			},
		},
	}

	return levels
}
