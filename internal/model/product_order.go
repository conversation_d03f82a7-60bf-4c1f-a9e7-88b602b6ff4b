package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	// Order Types
	ProductOrderTypeDefault int8 = 1 // 默认订单

	// Order Status
	ProductOrderStatusCancelled int8 = -1 // 取消
	ProductOrderStatusWaiting   int8 = 10 // 等待
	ProductOrderStatusRunning   int8 = 11 // 运行
	ProductOrderStatusCompleted int8 = 20 // 完成
)

// Order 表示系统中的产品订单
type Order struct {
	gorm.Model
	// 操作的管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager   *Manager  `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID    uint      `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	ProductID uint      `gorm:"type:int unsigned not null;index;comment:产品ID" json:"productId"`
	OrderSn   string    `gorm:"type:varchar(64);not null;uniqueIndex;comment:订单编号" json:"orderSn"`
	Money     float64   `gorm:"type:decimal(20,6) not null;comment:金额" json:"money"`
	Fee       float64   `gorm:"type:decimal(20,6) not null;comment:手续费" json:"fee"`
	Nums      float64   `gorm:"type:decimal(20,6) not null;comment:数量" json:"nums"`
	Type      int8      `gorm:"type:tinyint unsigned not null;default:1;index;comment:类型(1:默认)" json:"type"`
	Status    int8      `gorm:"type:tinyint not null;default:10;index;comment:订单状态(-1:取消,10:等待,11:运行,20:完成)" json:"status"`
	Data      string    `gorm:"type:text;comment:数据" json:"data"`
	ExpiredAt time.Time `gorm:"type:datetime(3);index;comment:过期时间" json:"expiredAt"`
}

// IsCancelled 检查订单是否已取消
func (o *Order) IsCancelled() bool {
	return o.Status == ProductOrderStatusCancelled
}

// IsWaiting 检查订单是否处于等待状态
func (o *Order) IsWaiting() bool {
	return o.Status == ProductOrderStatusWaiting
}

// IsRunning 检查订单是否处于运行状态
func (o *Order) IsRunning() bool {
	return o.Status == ProductOrderStatusRunning
}

// IsCompleted 检查订单是否已完成
func (o *Order) IsCompleted() bool {
	return o.Status == ProductOrderStatusCompleted
}

// Cancel 取消订单
func (o *Order) Cancel() {
	o.Status = ProductOrderStatusCancelled
}

// Complete 完成订单
func (o *Order) Complete() {
	o.Status = ProductOrderStatusCompleted
}

// SetRunning 设置订单为运行状态
func (o *Order) SetRunning() {
	o.Status = ProductOrderStatusRunning
}
