package model

import (
	"gorm.io/gorm"
	"time"
)

const (
	// Order Types
	ProductOrderTypeDefault int8 = 1 // 默认订单

	// Order Status
	ProductOrderStatusCancelled int8 = -1 // 取消
	ProductOrderStatusWaiting   int8 = 10 // 等待
	ProductOrderStatusRunning   int8 = 11 // 运行
	ProductOrderStatusCompleted int8 = 20 // 完成
)

// Order represents a product order in the system
type Order struct {
	gorm.Model
	MerchantID uint      `gorm:"type:int unsigned not null;index;comment:商户ID" json:"merchantId"`
	UserID     uint      `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	ProductID  uint      `gorm:"type:int unsigned not null;index;comment:产品ID" json:"productId"`
	OrderSn    string    `gorm:"type:varchar(64);not null;uniqueIndex;comment:订单编号" json:"orderSn"`
	Money      float64   `gorm:"type:decimal(20,6) not null;comment:金额" json:"money"`
	Fee        float64   `gorm:"type:decimal(20,6) not null;comment:手续费" json:"fee"`
	Nums       float64   `gorm:"type:decimal(20,6) not null;comment:数量" json:"nums"`
	Type       int8      `gorm:"type:tinyint unsigned not null;default:1;index;comment:类型(1:默认)" json:"type"`
	Status     int8      `gorm:"type:tinyint not null;default:10;index;comment:订单状态(-1:取消,10:等待,11:运行,20:完成)" json:"status"`
	Data       string    `gorm:"type:text;comment:数据" json:"data"`
	ExpiredAt  time.Time `gorm:"type:datetime(3);index;comment:过期时间" json:"expiredAt"`
}
