package model

import (
	"database/sql/driver"
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/goccy/go-json"
)

const (
	// Sex constants
	SexUnknown int8 = -1 // 未知性别
	SexMale    int8 = 1  // 男性
	SexFemale  int8 = 2  // 女性

	// User type constants
	UserTypeVirtual int8 = -1 // 虚拟用户
	UserTypeDefault int8 = 1  // 普通用户

	// User status constants
	UserStatusFrozen int8 = -1 // 冻结状态
	UserStatusActive int8 = 10 // 激活状态

	UserRegisterTypeEmail     = 1 // 邮箱注册
	UserRegisterTypeTelephone = 2 // 手机注册
	UserRegisterTypeUsername  = 3 // 用户名注册
)

// User 用户表
type User struct {
	gorm.Model
	ChannelID       uint      `gorm:"type:int unsigned not null;index;comment:渠道ID" json:"channelId"`
	ParentID        uint      `gorm:"type:int unsigned not null;index;comment:父级ID" json:"parentId"`
	CountryID       uint      `gorm:"type:int unsigned not null;index;comment:国家ID" json:"countryId"`
	Username        string    `gorm:"type:varchar(60) not null;uniqueIndex:idx_admin_username;comment:用户名" json:"username"`
	Nickname        string    `gorm:"type:varchar(60) not null;index;comment:昵称" json:"nickname"`
	Email           string    `gorm:"type:varchar(60);uniqueIndex:idx_admin_email;comment:邮箱;default:null" json:"email"`
	Telephone       string    `gorm:"type:varchar(50);uniqueIndex:idx_admin_telephone;comment:手机号码;default:null" json:"telephone"`
	Lang            string    `gorm:"type:varchar(60) not null;index;comment:语言;" json:"lang"`
	Avatar          string    `gorm:"type:varchar(255) not null;comment:头像" json:"avatar"`
	Score           int       `gorm:"type:int not null;default:100;comment:信用分" json:"score"`
	Sex             int8      `gorm:"type:tinyint not null;default:-1;comment:性别(-1:未知,1:男,2:女)" json:"sex"`
	Birthday        time.Time `gorm:"type:date;comment:生日" json:"birthday"`
	Password        string    `gorm:"type:varchar(255) not null;comment:密码" json:"-"`
	SecurityKey     string    `gorm:"type:varchar(255) not null;comment:密钥" json:"-"`
	FrozenAmount    float64   `gorm:"type:decimal(16,2) not null;default:0;comment:冻结金额" json:"frozenAmount"`
	AvailableAmount float64   `gorm:"type:decimal(16,2) not null;default:0;comment:可用金额" json:"availableAmount"`
	InviteCode      string    `gorm:"type:varchar(20);uniqueIndex;comment:邀请码" json:"inviteCode"`
	Type            int8      `gorm:"type:tinyint not null;default:1;index;comment:类型(-1:虚拟用户,1:普通用户,10:渠道用户)" json:"type"`
	Status          int8      `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:冻结,10:激活)" json:"status"`
	LastLoginAt     time.Time `gorm:"type:timestamp;comment:登录时间" json:"lastLoginAt"`
	LastLoginIP     string    `gorm:"type:varchar(45);comment:登录IP" json:"lastLoginIP"`
	SignupIP        string    `gorm:"type:varchar(45);comment:注册IP" json:"signupIP"`
	Desc            string    `gorm:"type:text;comment:详情" json:"desc"`
	Data            UserData  `gorm:"type:json;comment:数据" json:"data"`
}

// UserData 用户数据
type UserData struct {
	Address     string `json:"address"`     // 地址
	IDCardNo    string `json:"idCardNo"`    // 身份证号
	Occupation  string `json:"occupation"`  // 职业
	Education   string `json:"education"`   // 教育程度
	Interests   string `json:"interests"`   // 兴趣爱好
	SocialMedia string `json:"socialMedia"` // 社交媒体账号
}

// Value implements the driver.Valuer interface
func (d UserData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *UserData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

func (u *User) IsFrozen() bool {
	return u.Status == UserStatusFrozen
}

func (u *User) IsVirtual() bool {
	return u.Type == UserTypeVirtual
}

func (u *User) Validate() error {
	if u.Username == "" {
		return errors.New("username is required")
	}
	if len(u.Username) > 60 {
		return errors.New("username length must be less than 60")
	}
	if u.Status != UserStatusActive && u.Status != UserStatusFrozen {
		return errors.New("invalid status")
	}
	if u.Type != UserTypeDefault && u.Type != UserTypeVirtual {
		return errors.New("invalid type")
	}
	return nil
}

func (u *User) BeforeCreate(tx *gorm.DB) error {
	return u.Validate()
}

func (u *User) BeforeUpdate(tx *gorm.DB) error {
	return u.Validate()
}

func (u *User) GetUserData() *UserData {
	return &u.Data
}
