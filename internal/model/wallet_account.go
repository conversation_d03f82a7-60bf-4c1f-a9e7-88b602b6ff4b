package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	// AccountType 账户类型
	AccountTypeBalance int8 = 1  // 余额账户
	AccountTypeAsset   int8 = 11 // 资产账户

	// AccountStatus 账户状态
	AccountStatusDisabled int8 = -1 // 禁用
	AccountStatusEnabled  int8 = 10 // 启用
)

// WalletAccount 钱包账户管理
type WalletAccount struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager   *Manager          `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID    uint              `gorm:"type:int unsigned not null;comment:用户ID" json:"userId"`
	PaymentID uint              `gorm:"type:int unsigned not null;comment:支付ID" json:"paymentId"`
	Name      string            `gorm:"type:varchar(255) not null;comment:名称" json:"name"`
	Type      int8              `gorm:"type:tinyint not null;default:1;comment:类型(1:余额,11:资产)" json:"type"`
	Status    int8              `gorm:"type:tinyint not null;default:10;comment:状态(-1:禁用,10:启用)" json:"status"`
	Data      WalletPaymentData `gorm:"type:json;comment:数据" json:"data"`
}

// BeforeCreate GORM钩子：在创建WalletAccount之前调用Validate方法
func (wa *WalletAccount) BeforeCreate(tx *gorm.DB) (err error) {
	return wa.Validate()
}

// BeforeUpdate GORM钩子：在更新WalletAccount之前调用Validate方法
func (wa *WalletAccount) BeforeUpdate(tx *gorm.DB) (err error) {
	return wa.Validate()
}

// Validate 校验 WalletAccount 字段的合法性
func (wa *WalletAccount) Validate() error {
	if wa.UserID == 0 {
		return errors.New("用户ID不能为空")
	}
	if wa.Name == "" {
		return errors.New("账户名称不能为空")
	}
	// 校验 Type 字段
	if !wa.IsTypeBalance() && !wa.IsTypeAsset() {
		return errors.New("账户类型不合法")
	}
	// 校验 Status 字段
	if !wa.IsStatusDisabled() && !wa.IsStatusEnabled() {
		return errors.New("账户状态不合法")
	}
	// 可以添加更多针对 Data 字段的校验逻辑
	return nil
}

// IsTypeBalance 判断是否为余额账户
func (wa *WalletAccount) IsTypeBalance() bool {
	return wa.Type == AccountTypeBalance
}

// IsTypeAsset 判断是否为资产账户
func (wa *WalletAccount) IsTypeAsset() bool {
	return wa.Type == AccountTypeAsset
}

// IsStatusDisabled 判断账户是否被禁用
func (wa *WalletAccount) IsStatusDisabled() bool {
	return wa.Status == AccountStatusDisabled
}

// IsStatusEnabled 判断账户是否启用
func (wa *WalletAccount) IsStatusEnabled() bool {
	return wa.Status == AccountStatusEnabled
}
