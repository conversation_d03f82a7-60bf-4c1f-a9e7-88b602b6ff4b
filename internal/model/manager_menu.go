package model

import (
	"app/pkg/logger"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

const (
	// 菜单类型
	ManagerMenuTypeDirectory int = 1 // 目录
	ManagerMenuTypeMenu      int = 2 // 菜单
	ManagerMenuTypeButton    int = 3 // 按钮

	// 菜单状态
	ManagerMenuStatusEnabled  int = 1 // 启用
	ManagerMenuStatusDisabled int = 0 // 禁用

	// 是否外链
	ManagerMenuIsFrameYes int = 1 // 是
	ManagerMenuIsFrameNo  int = 0 // 否

	// 是否缓存
	ManagerMenuIsCacheYes int = 1 // 是
	ManagerMenuIsCacheNo  int = 0 // 否

	// 是否显示
	ManagerMenuIsVisibleYes int = 1 // 是
	ManagerMenuIsVisibleNo  int = 0 // 否
)

// ManagerMenu 管理菜单
type ManagerMenu struct {
	BaseModel

	// 父级ID
	ParentID uint `gorm:"index;default:0;comment:'父级ID'" json:"parent_id"`
	// 菜单名称
	Name string `gorm:"type:varchar(50);not null;comment:'菜单名称'" json:"name"`
	// 菜单标题
	Title string `gorm:"type:varchar(50);not null;comment:'菜单标题'" json:"title"`
	// 菜单图标
	Icon string `gorm:"type:varchar(50);comment:'菜单图标'" json:"icon"`
	// 菜单类型（1目录 2菜单 3按钮）
	Type int `gorm:"type:smallint;not null;default:1;comment:'菜单类型'" json:"type"`
	// 路由地址
	Path string `gorm:"type:varchar(200);comment:'路由地址'" json:"path"`
	// 组件路径
	Component string `gorm:"type:varchar(255);comment:'组件路径'" json:"component"`
	// 权限标识
	Permission string `gorm:"type:varchar(100);comment:'权限标识'" json:"permission"`
	// 菜单状态（1启用 0禁用）
	Status int `gorm:"type:smallint;not null;default:1;comment:'菜单状态'" json:"status"`
	// 是否外链（1是 0否）
	Frame int `gorm:"type:smallint;not null;default:0;comment:'是否外链'" json:"frame"`
	// 是否缓存（1是 0否）
	Cache int `gorm:"type:smallint;not null;default:0;comment:'是否缓存'" json:"cache"`
	// 是否显示（1是 0否）
	Visible int `gorm:"type:smallint;not null;default:1;comment:'是否显示'" json:"visible"`
	// 排序
	Sort int `gorm:"type:int;not null;default:0;comment:'排序'" json:"sort"`
	// 备注
	Remark string `gorm:"type:varchar(255);comment:'备注'" json:"remark"`
	// 子菜单
	Children []*ManagerMenu `gorm:"-" json:"children,omitempty"`
}

// IsDirectory 是否是目录
func (m *ManagerMenu) IsDirectory() bool {
	return m.Type == ManagerMenuTypeDirectory
}

// IsMenu 是否是菜单
func (m *ManagerMenu) IsMenu() bool {
	return m.Type == ManagerMenuTypeMenu
}

// IsButton 是否是按钮
func (m *ManagerMenu) IsButton() bool {
	return m.Type == ManagerMenuTypeButton
}

// IsEnabled 是否启用
func (m *ManagerMenu) IsEnabled() bool {
	return m.Status == ManagerMenuStatusEnabled
}

// IsDisabled 是否禁用
func (m *ManagerMenu) IsDisabled() bool {
	return m.Status == ManagerMenuStatusDisabled
}

// IsFrame 是否是外链
func (m *ManagerMenu) IsFrame() bool {
	return m.Frame == ManagerMenuIsFrameYes
}

// IsCache 是否缓存
func (m *ManagerMenu) IsCache() bool {
	return m.Cache == ManagerMenuIsCacheYes
}

// IsVisible 是否显示
func (m *ManagerMenu) IsVisible() bool {
	return m.Visible == ManagerMenuIsVisibleYes
}

// Validate 验证菜单数据
func (m *ManagerMenu) Validate() error {
	if m.Name == "" {
		return errors.New("name is required")
	}
	if m.Title == "" {
		return errors.New("title is required")
	}
	if m.Type < ManagerMenuTypeDirectory || m.Type > ManagerMenuTypeButton {
		return errors.New("invalid type")
	}
	if m.Status != ManagerMenuStatusEnabled && m.Status != ManagerMenuStatusDisabled {
		return errors.New("invalid status")
	}
	if m.Frame != ManagerMenuIsFrameYes && m.Frame != ManagerMenuIsFrameNo {
		return errors.New("invalid frame")
	}
	if m.Cache != ManagerMenuIsCacheYes && m.Cache != ManagerMenuIsCacheNo {
		return errors.New("invalid cache")
	}
	if m.Visible != ManagerMenuIsVisibleYes && m.Visible != ManagerMenuIsVisibleNo {
		return errors.New("invalid visible")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (m *ManagerMenu) BeforeCreate(tx *gorm.DB) error {
	return m.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (m *ManagerMenu) BeforeUpdate(tx *gorm.DB) error {
	return m.Validate()
}

// Enable 启用菜单
func (m *ManagerMenu) Enable() {
	m.Status = ManagerMenuStatusEnabled
}

// Disable 禁用菜单
func (m *ManagerMenu) Disable() {
	m.Status = ManagerMenuStatusDisabled
}

// SetFrame 设置是否外链
func (m *ManagerMenu) SetFrame(isFrame bool) {
	if isFrame {
		m.Frame = ManagerMenuIsFrameYes
	} else {
		m.Frame = ManagerMenuIsFrameNo
	}
}

// SetCache 设置是否缓存
func (m *ManagerMenu) SetCache(isCache bool) {
	if isCache {
		m.Cache = ManagerMenuIsCacheYes
	} else {
		m.Cache = ManagerMenuIsCacheNo
	}
}

// SetVisible 设置是否显示
func (m *ManagerMenu) SetVisible(isVisible bool) {
	if isVisible {
		m.Visible = ManagerMenuIsVisibleYes
	} else {
		m.Visible = ManagerMenuIsVisibleNo
	}
}

// AddChild 添加子菜单
func (m *ManagerMenu) AddChild(child *ManagerMenu) {
	if m.Children == nil {
		m.Children = make([]*ManagerMenu, 0)
	}
	m.Children = append(m.Children, child)
}

// GetChildren 获取子菜单
func (m *ManagerMenu) GetChildren() []*ManagerMenu {
	return m.Children
}

// HasChildren 是否有子菜单
func (m *ManagerMenu) HasChildren() bool {
	return len(m.Children) > 0
}

// IsRoot 是否是根菜单
func (m *ManagerMenu) IsRoot() bool {
	return m.ParentID == 0
}

// InitManagerMenu 初始化管理菜单
func InitManagerMenu(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 ManagerMenu 表
	if err := db.AutoMigrate(&ManagerMenu{}); err != nil {
		return fmt.Errorf("failed to auto migrate ManagerMenu table in init: %v", err)
	}

	// 检查是否已存在系统管理目录
	var count int64
	result := db.Model(&ManagerMenu{}).Where("name = ?", "system").Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check ManagerMenu count in init: %v", result.Error)
	}

	// 如果没有数据，创建默认菜单
	if count == 0 {
		// 商户管理目录
		merchantMenu := &ManagerMenu{
			Name:     "merchant",
			Title:    "商户管理",
			Icon:     "shop",
			Type:     ManagerMenuTypeDirectory,
			Path:     "/merchant",
			Status:   ManagerMenuStatusEnabled,
			Sort:     1,
			Children: make([]*ManagerMenu, 0),
		}

		// 商户列表菜单
		merchantListMenu := &ManagerMenu{
			Name:       "merchant-list",
			Title:      "商户列表",
			Icon:       "list",
			Type:       ManagerMenuTypeMenu,
			Path:       "list",
			Component:  "merchant/list/index",
			Status:     ManagerMenuStatusEnabled,
			Sort:       1,
			Permission: "merchant:list",
		}

		// 商户域名菜单
		merchantDomainMenu := &ManagerMenu{
			Name:       "merchant-domain",
			Title:      "商户域名",
			Icon:       "link",
			Type:       ManagerMenuTypeMenu,
			Path:       "domain",
			Component:  "merchant/domain/index",
			Status:     ManagerMenuStatusEnabled,
			Sort:       2,
			Permission: "merchant:domain",
		}

		// 系统管理目录
		systemMenu := &ManagerMenu{
			Name:     "system",
			Title:    "系统管理",
			Icon:     "setting",
			Type:     ManagerMenuTypeDirectory,
			Path:     "/system",
			Status:   ManagerMenuStatusEnabled,
			Sort:     2,
			Children: make([]*ManagerMenu, 0),
		}

		// 管理员列表菜单
		managerListMenu := &ManagerMenu{
			Name:       "manager-list",
			Title:      "管理员列表",
			Icon:       "user",
			Type:       ManagerMenuTypeMenu,
			Path:       "manager",
			Component:  "system/manager/index",
			Status:     ManagerMenuStatusEnabled,
			Sort:       1,
			Permission: "system:manager:list",
		}

		// 角色管理菜单
		roleMenu := &ManagerMenu{
			Name:       "role",
			Title:      "角色管理",
			Icon:       "peoples",
			Type:       ManagerMenuTypeMenu,
			Path:       "role",
			Component:  "system/role/index",
			Status:     ManagerMenuStatusEnabled,
			Sort:       2,
			Permission: "system:role:list",
		}

		// 菜单管理菜单
		menuMenu := &ManagerMenu{
			Name:       "menu",
			Title:      "菜单管理",
			Icon:       "tree-table",
			Type:       ManagerMenuTypeMenu,
			Path:       "menu",
			Component:  "system/menu/index",
			Status:     ManagerMenuStatusEnabled,
			Sort:       3,
			Permission: "system:menu:list",
		}

		// 操作日志菜单
		logMenu := &ManagerMenu{
			Name:       "log",
			Title:      "操作日志",
			Icon:       "documentation",
			Type:       ManagerMenuTypeMenu,
			Path:       "log",
			Component:  "system/log/index",
			Status:     ManagerMenuStatusEnabled,
			Sort:       4,
			Permission: "system:log:list",
		}

		// 添加商户管理子菜单
		merchantMenu.AddChild(merchantListMenu)
		merchantMenu.AddChild(merchantDomainMenu)

		// 添加系统管理子菜单
		systemMenu.AddChild(managerListMenu)
		systemMenu.AddChild(roleMenu)
		systemMenu.AddChild(menuMenu)
		systemMenu.AddChild(logMenu)

		// 使用事务来确保数据一致性
		if err := db.Transaction(func(tx *gorm.DB) error {
			// 保存商户管理目录
			if err := tx.Create(merchantMenu).Error; err != nil {
				return err
			}

			// 保存商户管理子菜单
			for _, child := range merchantMenu.Children {
				child.ParentID = merchantMenu.ID
				if err := tx.Create(child).Error; err != nil {
					return err
				}
			}

			// 保存系统管理目录
			if err := tx.Create(systemMenu).Error; err != nil {
				return err
			}

			// 保存系统管理子菜单
			for _, child := range systemMenu.Children {
				child.ParentID = systemMenu.ID
				if err := tx.Create(child).Error; err != nil {
					return err
				}
			}

			return nil
		}); err != nil {
			return fmt.Errorf("failed to create initial menu in init: %v", err)
		}

		appLogger.Info("Initial menu created successfully.")
	}

	return nil
}
