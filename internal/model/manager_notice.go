package model

import (
	"app/pkg/logger"
	"fmt"

	"gorm.io/gorm"
)

// 通知已读状态常量
const (
	ManagerNoticeTypeSystem   = 1 // 系统通知
	ManagerNoticeTypeActivity = 2 // 活动通知
	ManagerNoticeTypeOther    = 3 // 其他通知

	ManagerNoticeCategoryAnnouncement = 1 // 公告
	ManagerNoticeCategoryReminder     = 2 // 提醒
	ManagerNoticeCategoryWarning      = 3 // 警告
	ManagerNoticeCategoryUpdate       = 4 // 更新

	ManagerNoticePriorityUrgent    = 1 // 紧急
	ManagerNoticePriorityImportant = 2 // 重要
	ManagerNoticePriorityNormal    = 3 // 普通
	ManagerNoticePriorityLow       = 4 // 低

	ManagerNoticeStatusDraft     = 0 // 草稿
	ManagerNoticeStatusPublished = 1 // 已发布
	ManagerNoticeStatusExpired   = 2 // 已过期
	ManagerNoticeStatusDeleted   = 3 // 已删除

	ManagerNoticeSourceSystem = "system" // 系统
	ManagerNoticeSourceAdmin  = "admin"  // 管理员
	ManagerNoticeSourceAPI    = "api"    // 接口

	ManagerNoticeUnread = 0 // 未读
	ManagerNoticeRead   = 1 // 已读
)

// ManagerNotice 管理系统通知表
type ManagerNotice struct {
	BaseModel

	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	// 通知标题
	Title string `gorm:"size:100;not null" json:"title"`
	// 通知内容
	Content string `gorm:"type:text;not null" json:"content"`
	// 通知类型：1=系统通知，2=活动通知，3=其他通知
	Type int `gorm:"default:1" json:"type"`
	// 通知分类：1=公告，2=提醒，3=警告，4=更新
	Category int `gorm:"default:1" json:"category"`
	// 优先级：1=紧急，2=重要，3=普通，4=低
	Priority int `gorm:"default:2" json:"priority"`
	// 状态：0=草稿，1=已发布，2=已过期，3=已删除
	Status int `gorm:"default:1" json:"status"`
	// 是否已读：0=未读，1=已读
	IsRead int `gorm:"default:0" json:"is_read"`
	// 通知来源：system=系统，admin=管理员，api=接口
	Source string `gorm:"size:50" json:"source"`
	// 通知标签，多个标签用逗号分隔
	Tags string `gorm:"size:255" json:"tags"`
}

// InitManagerNotice 初始化通知相关常量
func InitManagerNotice(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 ManagerNotice 表
	if err := db.AutoMigrate(&ManagerNotice{}); err != nil {
		return fmt.Errorf("failed to auto migrate ManagerNotice table in init: %v", err)
	}

	appLogger.Info("ManagerNotice table initialized successfully.")
	return nil
}
