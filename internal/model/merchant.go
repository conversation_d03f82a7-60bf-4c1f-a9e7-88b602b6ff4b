package model

import (
	"app/pkg/logger"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

const (
	// 商户状态
	MerchantStatusDisabled int = 0 // 禁用
	MerchantStatusActive   int = 1 // 正常
	MerchantStatusPending  int = 2 // 待激活/待审批
	MerchantStatusExpired  int = 3 // 已过期

	// 商户类型/计划
	MerchantTypeBasic      int = 1 // 基础版
	MerchantTypePro        int = 2 // 专业版
	MerchantTypeEnterprise int = 3 // 企业版
)

// Merchant 商户
type Merchant struct {
	BaseModel

	// 商户名称，唯一索引
	Name string `gorm:"type:varchar(100);uniqueIndex;not null;comment:'名称'" json:"name"`
	// 商户备注信息
	Remark string `gorm:"type:varchar(255);comment:'备注'" json:"remark"`
	// 商户配置信息，JSON格式
	Config string `gorm:"type:text;comment:'配置(JSON等)'" json:"config,omitempty"`
	// 商户状态：0-禁用，1-正常，2-待激活，3-已过期
	Status int `gorm:"type:smallint;not null;default:1;comment:'状态'" json:"status"`
	// 商户到期时间
	ExpiredAt *time.Time `gorm:"comment:'到期日期'" json:"expired_at,omitempty"`
	// 商户类型/计划：1-基础版，2-专业版，3-企业版
	Type int `gorm:"type:int;not null;default:1;comment:'类型/计划'" json:"tenantType"`
	// 数据库配置信息，JSON格式
	DBConfigJson MerchantDBConfig `gorm:"type:json;serializer:json;comment:'数据库配置(JSON格式)'" json:"db_config_json,omitempty"`
	// JWT配置信息，JSON格式
	JWTConfig MerchantJWTConfig `gorm:"type:json;serializer:json;comment:'JWT配置(JSON格式)'" json:"jwt_config_json,omitempty"`
	// 服务器和服务配置信息，JSON格式
	ServerConfig MerchantServerConfig `gorm:"type:json;serializer:json;comment:'服务器及服务配置(JSON格式)'" json:"server_config_json,omitempty"`
	// 关联的域名列表
	Domains []MerchantDomain `gorm:"foreignKey:MerchantID;references:ID"`
}

// MerchantDBConfig 商户数据库配置
type MerchantDBConfig struct {
	// 数据库类型 (e.g., mysql, postgres, mongodb)
	Type string `yaml:"type"`
	// 数据库主机地址
	Host string `yaml:"host"`
	// 数据库端口
	Port int `yaml:"port"`
	// 数据库名称
	Name string `yaml:"name"`
	// 数据库用户名
	Username string `yaml:"username"`
	// 数据库密码 (!!!生产环境勿直接写在此!!!)
	Password string `yaml:"password"`
	// 字符集 (MySQL常用)
	Charset string `yaml:"charset,omitempty"`
	// SSL模式 (PostgreSQL常用)
	SSLMode string `yaml:"sslmode,omitempty"`
}

// MerchantJWTConfig 商户JWT配置
type MerchantJWTConfig struct {
	// 用于签名和验证JWT的密钥 (!!!生产环境请使用强随机密钥且妥善保管!!!)
	SecretKey string `yaml:"secret_key"`
	// token 有效期，单位分钟
	ExpirationMinutes int `yaml:"expiration_minutes"`
	// token的签发者 (可选)
	Issuer string `yaml:"issuer,omitempty"`
	// token的接收者 (可选)
	Audience string `yaml:"audience,omitempty"`
	// 签名算法 (e.g., HS256)
	SigningMethod string `yaml:"signing_method"`
	// 允许的最大会话数
	MaxSessions int `yaml:"max_sessions"`
}

// MerchantServerConfig 商户服务器及服务配置
type MerchantServerConfig struct {
	// SSH 服务器地址
	SSHHost string `json:"ssh_host,omitempty"`
	// SSH 端口 (默认为 22)
	SSHPort int `json:"ssh_port,omitempty"`
	// SSH 用户名
	SSHUser string `json:"ssh_user,omitempty"`
	// 加密后的 SSH 密码
	SSHPasswordEncrypted string `json:"ssh_password_encrypted,omitempty"`
}

// IsActive 检查商户是否处于活跃状态
func (m *Merchant) IsActive() bool {
	return m.Status == MerchantStatusActive
}

// IsDisabled 检查商户是否被禁用
func (m *Merchant) IsDisabled() bool {
	return m.Status == MerchantStatusDisabled
}

// IsPending 检查商户是否待激活
func (m *Merchant) IsPending() bool {
	return m.Status == MerchantStatusPending
}

// IsExpired 检查商户是否已过期
func (m *Merchant) IsExpired() bool {
	return m.Status == MerchantStatusExpired
}

// IsBasic 检查是否是基础版
func (m *Merchant) IsBasic() bool {
	return m.Type == MerchantTypeBasic
}

// IsPro 检查是否是专业版
func (m *Merchant) IsPro() bool {
	return m.Type == MerchantTypePro
}

// IsEnterprise 检查是否是企业版
func (m *Merchant) IsEnterprise() bool {
	return m.Type == MerchantTypeEnterprise
}

// IsExpiredByTime 检查是否已过期（基于时间）
func (m *Merchant) IsExpiredByTime() bool {
	if m.ExpiredAt == nil {
		return false
	}
	return m.ExpiredAt.Before(time.Now())
}

// Validate 验证商户数据
func (m *Merchant) Validate() error {
	if m.Name == "" {
		return errors.New("name is required")
	}
	if len(m.Name) > 100 {
		return errors.New("name length must be less than 100")
	}
	if m.Status < MerchantStatusDisabled || m.Status > MerchantStatusExpired {
		return errors.New("invalid status")
	}
	if m.Type < MerchantTypeBasic || m.Type > MerchantTypeEnterprise {
		return errors.New("invalid type")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (m *Merchant) BeforeCreate(tx *gorm.DB) error {
	return m.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (m *Merchant) BeforeUpdate(tx *gorm.DB) error {
	return m.Validate()
}

// GetDBConfig 获取数据库配置
func (m *Merchant) GetDBConfig() *MerchantDBConfig {
	return &m.DBConfigJson
}

// GetJWTConfig 获取JWT配置
func (m *Merchant) GetJWTConfig() *MerchantJWTConfig {
	return &m.JWTConfig
}

// GetServerConfig 获取服务器配置
func (m *Merchant) GetServerConfig() *MerchantServerConfig {
	return &m.ServerConfig
}

// GetDomains 获取所有域名
func (m *Merchant) GetDomains() []MerchantDomain {
	return m.Domains
}

// GetActiveDomains 获取所有活跃的域名
func (m *Merchant) GetActiveDomains() []MerchantDomain {
	var activeDomains []MerchantDomain
	for _, domain := range m.Domains {
		if domain.Status == 1 { // 假设 1 是活跃状态
			activeDomains = append(activeDomains, domain)
		}
	}
	return activeDomains
}

// HasDomain 检查是否拥有指定域名
func (m *Merchant) HasDomain(domain string) bool {
	for _, d := range m.Domains {
		if d.Domain == domain {
			return true
		}
	}
	return false
}

// AddDomain 添加域名
func (m *Merchant) AddDomain(domain MerchantDomain) {
	m.Domains = append(m.Domains, domain)
}

// RemoveDomain 移除域名
func (m *Merchant) RemoveDomain(domain string) {
	var newDomains []MerchantDomain
	for _, d := range m.Domains {
		if d.Domain != domain {
			newDomains = append(newDomains, d)
		}
	}
	m.Domains = newDomains
}

// InitMerchant 初始化商户
func InitMerchant(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 Merchant 和 MerchantDomain 表
	if err := db.AutoMigrate(&Merchant{}, &MerchantDomain{}); err != nil {
		return fmt.Errorf("failed to auto migrate Merchant and MerchantDomain tables in init: %v", err)
	}

	// 检查是否已存在测试商户
	var count int64
	result := db.Model(&Merchant{}).Where("name = ?", "test-merchant").Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check Merchant count in init: %v", result.Error)
	}

	// 如果没有数据，创建测试商户
	if count == 0 {
		// 设置过期时间为一年后
		expiredAt := time.Now().AddDate(1, 0, 0)

		testMerchant := &Merchant{
			Name:      "test-merchant",
			Remark:    "测试商户",
			Status:    MerchantStatusActive,
			Type:      MerchantTypePro,
			ExpiredAt: &expiredAt,
			DBConfigJson: MerchantDBConfig{
				Type:     "mysql",
				Host:     "localhost",
				Port:     3306,
				Name:     "test_db",
				Username: "test_user",
				Password: "test_password",
				Charset:  "utf8mb4",
			},
			JWTConfig: MerchantJWTConfig{
				SecretKey:         "test-secret-key",
				ExpirationMinutes: 1440, // 24小时
				Issuer:            "test-issuer",
				SigningMethod:     "HS256",
				MaxSessions:       10,
			},
			ServerConfig: MerchantServerConfig{
				SSHHost:              "localhost",
				SSHPort:              22,
				SSHUser:              "test_user",
				SSHPasswordEncrypted: "encrypted_password",
			},
		}

		// 创建测试商户
		if err := db.Create(testMerchant).Error; err != nil {
			return fmt.Errorf("failed to create test merchant in init: %v", err)
		}

		// 创建测试域名
		testDomain := &MerchantDomain{
			MerchantID: testMerchant.ID,
			Domain:     "test.example.com",
			Type:       DomainTypePrimary,
			Status:     DomainStatusActive,
			Remark:     "测试域名",
		}

		if err := db.Create(testDomain).Error; err != nil {
			return fmt.Errorf("failed to create test domain in init: %v", err)
		}

		appLogger.Info("Test merchant and domain created successfully.")
	}

	return nil
}
