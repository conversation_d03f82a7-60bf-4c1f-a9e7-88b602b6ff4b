package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	// UserAssetStatusDisabled 用户资产状态：禁用
	UserAssetStatusDisabled int8 = -1
	// UserAssetStatusEnabled 用户资产状态：启用
	UserAssetStatusEnabled int8 = 10
)

// UserAssets 用户资产
type UserAssets struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager         *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID          uint     `gorm:"type:int unsigned not null;uniqueIndex:idx_user_asset;comment:用户ID" json:"userId"`
	AssetsID        uint     `gorm:"type:int unsigned not null;uniqueIndex:idx_user_asset;comment:资产ID" json:"assetsId"`
	FrozenAmount    float64  `gorm:"type:decimal(16,4) not null;default:0;comment:冻结金额" json:"frozenAmount"`
	AvailableAmount float64  `gorm:"type:decimal(16,4) not null;default:0;comment:可用金额" json:"availableAmount"`
	Status          int8     `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
}

// IsEnabled 检查用户资产是否启用
func (a *UserAssets) IsEnabled() bool {
	return a.Status == UserAssetStatusEnabled
}

// IsDisabled 检查用户资产是否禁用
func (a *UserAssets) IsDisabled() bool {
	return a.Status == UserAssetStatusDisabled
}

// Enable 启用用户资产
func (a *UserAssets) Enable() {
	a.Status = UserAssetStatusEnabled
}

// Disable 禁用用户资产
func (a *UserAssets) Disable() {
	a.Status = UserAssetStatusDisabled
}

// GetTotalAmount 获取总金额（可用金额 + 冻结金额）
func (a *UserAssets) GetTotalAmount() float64 {
	return a.AvailableAmount + a.FrozenAmount
}

// HasSufficientAvailableAmount 检查是否有足够的可用金额
func (a *UserAssets) HasSufficientAvailableAmount(amount float64) bool {
	return a.AvailableAmount >= amount
}

// FreezeAmount 冻结指定金额
func (a *UserAssets) FreezeAmount(amount float64) error {
	if amount <= 0 {
		return errors.New("冻结金额必须大于0")
	}
	if a.AvailableAmount < amount {
		return errors.New("可用金额不足")
	}
	a.AvailableAmount -= amount
	a.FrozenAmount += amount
	return nil
}

// UnfreezeAmount 解冻指定金额
func (a *UserAssets) UnfreezeAmount(amount float64) error {
	if amount <= 0 {
		return errors.New("解冻金额必须大于0")
	}
	if a.FrozenAmount < amount {
		return errors.New("冻结金额不足")
	}
	a.FrozenAmount -= amount
	a.AvailableAmount += amount
	return nil
}

// Validate 校验用户资产数据
func (a *UserAssets) Validate() error {
	if a.UserID == 0 {
		return errors.New("用户ID不能为空")
	}
	if a.AssetsID == 0 {
		return errors.New("资产ID不能为空")
	}
	if a.FrozenAmount < 0 {
		return errors.New("冻结金额不能为负数")
	}
	if a.AvailableAmount < 0 {
		return errors.New("可用金额不能为负数")
	}
	return nil
}

// BeforeCreate GORM hook for data validation and pre-processing
func (a *UserAssets) BeforeCreate(tx *gorm.DB) (err error) {
	return a.Validate()
}

// BeforeUpdate GORM hook for data validation
func (a *UserAssets) BeforeUpdate(tx *gorm.DB) (err error) {
	return a.Validate()
}
