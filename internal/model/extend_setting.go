package model

import (
	"gorm.io/gorm"
)

const (
	ExtendSettingGroupCategory = 1 // 分类
	ExtendSettingGroupProduct  = 2 // 产品
)

// ExtendSetting 扩展设置表
type ExtendSetting struct {
	gorm.Model
	// 操作的管理员ID
	ManagerID uint `gorm:"uniqueIndex:idx_admin_source_field;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager  *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	SourceID uint     `gorm:"type:int unsigned not null;uniqueIndex:idx_admin_source_field;comment:来源ID" json:"sourceId"`
	Name     string   `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	GroupID  uint     `gorm:"type:int unsigned not null;index;comment:分组ID" json:"groupId"`
	Type     string   `gorm:"type:varchar(50) not null;comment:input类型" json:"type"`
	Field    string   `gorm:"type:varchar(50) not null;uniqueIndex:idx_admin_source_field;comment:字段名" json:"field"`
	Value    string   `gorm:"type:text;comment:值" json:"value"`
}
