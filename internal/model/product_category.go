package model

import (
	"app/pkg/logger"
	"database/sql/driver"
	"errors"
	"fmt"

	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

const (
	// 分类翻译前缀
	CategoryTranslatePrefix = "categoryName_%s"

	// 分类类型
	CategoryTypeCommodity int8 = 1 // 商品分类

	// 分类状态
	CategoryStatusDisabled int8 = -1 // 禁用
	CategoryStatusEnabled  int8 = 10 // 启用
)

// Category 商品分类表
type Category struct {
	gorm.Model
	ParentID   uint         `gorm:"type:int unsigned not null;index;comment:父级ID" json:"parentId"`
	MerchantID uint         `gorm:"type:int unsigned not null;uniqueIndex:idx_merchant_symbol;comment:管理ID" json:"merchantId"`
	Symbol     string       `gorm:"type:varchar(60) not null;uniqueIndex:idx_merchant_symbol;comment:标识" json:"symbol"`
	Type       int8         `gorm:"type:tinyint not null;default:1;index;comment:类型(1:商品类型)" json:"type"`
	Name       string       `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Icon       string       `gorm:"type:varchar(255);comment:图标" json:"icon"`
	Sort       int16        `gorm:"type:smallint not null;default:99;index;comment:排序" json:"sort"`
	Status     int8         `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Desc       string       `gorm:"type:text;comment:描述" json:"desc"`
	Data       CategoryData `gorm:"type:json;comment:数据" json:"data"`
}

// CategoryData 分类附加数据
type CategoryData struct{}

// Value implements the driver.Valuer interface
func (d CategoryData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *CategoryData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// IsEnabled 检查分类是否启用
func (c *Category) IsEnabled() bool {
	return c.Status == CategoryStatusEnabled
}

// IsDisabled 检查分类是否禁用
func (c *Category) IsDisabled() bool {
	return c.Status == CategoryStatusDisabled
}

// IsCommodityType 检查是否是商品分类
func (c *Category) IsCommodityType() bool {
	return c.Type == CategoryTypeCommodity
}

// Validate 验证分类数据
func (c *Category) Validate() error {
	if c.Symbol == "" {
		return errors.New("symbol is required")
	}
	if len(c.Symbol) > 60 {
		return errors.New("symbol length must be less than 60")
	}
	if c.Name == "" {
		return errors.New("name is required")
	}
	if len(c.Name) > 60 {
		return errors.New("name length must be less than 60")
	}
	if c.Status != CategoryStatusDisabled && c.Status != CategoryStatusEnabled {
		return errors.New("invalid status")
	}
	if c.Type != CategoryTypeCommodity {
		return errors.New("invalid type")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (c *Category) BeforeCreate(tx *gorm.DB) error {
	return c.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (c *Category) BeforeUpdate(tx *gorm.DB) error {
	return c.Validate()
}

// InitProductCategory 初始化分类
func InitProductCategory(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 Category 表
	if err := db.AutoMigrate(&Category{}); err != nil {
		return fmt.Errorf("failed to auto migrate Category table in init: %v", err)
	}

	// 检查表是否为空
	var count int64
	result := db.Model(&Category{}).Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check Category count in init: %v", result.Error)
	}

	// 如果表为空，添加初始数据
	if count == 0 {
		categories := initCategory()
		for _, v := range categories {
			v.Name = fmt.Sprintf(CategoryTranslatePrefix, v.Symbol)
		}

		if err := db.CreateInBatches(categories, len(categories)).Error; err != nil {
			return fmt.Errorf("failed to create initial categories in init: %v", err)
		}

		appLogger.Info("Initial categories created successfully.")
	}

	return nil
}

// initCategory 返回初始分类数据
func initCategory() []*Category {
	return []*Category{
		{
			ParentID:   0,
			MerchantID: 1,
			Symbol:     "electronics",
			Type:       CategoryTypeCommodity,
			Name:       "电子产品",
			Icon:       "icon-electronics",
			Sort:       10,
			Status:     CategoryStatusEnabled,
			Desc:       "电子产品分类",
			Data:       CategoryData{},
		},
		{
			ParentID:   0,
			MerchantID: 1,
			Symbol:     "clothing",
			Type:       CategoryTypeCommodity,
			Name:       "服装",
			Icon:       "icon-clothing",
			Sort:       20,
			Status:     CategoryStatusEnabled,
			Desc:       "服装分类",
			Data:       CategoryData{},
		},
		{
			ParentID:   0,
			MerchantID: 1,
			Symbol:     "food",
			Type:       CategoryTypeCommodity,
			Name:       "食品",
			Icon:       "icon-food",
			Sort:       30,
			Status:     CategoryStatusEnabled,
			Desc:       "食品分类",
			Data:       CategoryData{},
		},
	}
}
