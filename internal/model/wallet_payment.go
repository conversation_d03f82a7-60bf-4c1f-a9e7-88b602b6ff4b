package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

const (
	// 支付类型
	PaymentTypeBankCard   int8 = 1  // 银行卡
	PaymentTypeCrypto     int8 = 11 // 数字货币
	PaymentTypeThirdParty int8 = 21 // 第三方支付

	// 支付模式
	PaymentModeDeposit         int8 = 1  // 充值
	PaymentModeAssetDeposit    int8 = 2  // 资产充值
	PaymentModeWithdrawal      int8 = 11 // 提现
	PaymentModeAssetWithdrawal int8 = 12 // 资产提现
	PaymentModePay             int8 = 21 // 支付

	// 状态类型
	PaymentStatusDisabled int8 = -1 // 禁用
	PaymentStatusEnabled  int8 = 10 // 启用
)

// WalletPayment 钱包支付管理
type WalletPayment struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager   *Manager          `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	AssetsID  uint              `gorm:"type:int unsigned not null;default:0;index;comment:资产ID" json:"assetsId"`
	Name      string            `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Icon      string            `gorm:"type:varchar(255) not null;comment:图标" json:"icon"`
	Currency  string            `gorm:"type:varchar(60) not null;comment:货币符号" json:"currency"`
	Type      int8              `gorm:"type:tinyint not null;default:1;index;comment:类型(1:银行卡,11:数字货币,21:三方支付)" json:"type"`
	Mode      int8              `gorm:"type:tinyint not null;default:1;index;comment:模式(1:充值,2:资产充值,11:提现,12:资产提现,21:支付)" json:"mode"`
	MinAmount float64           `gorm:"type:decimal(16,2) not null;default:1;comment:最小金额" json:"minAmount"`
	MaxAmount float64           `gorm:"type:decimal(16,2) not null;default:1000000;comment:最大金额" json:"maxAmount"`
	StartTime string            `gorm:"type:varchar(60) not null;default:'00:00:00';comment:开始时间" json:"startTime"`
	EndTime   string            `gorm:"type:varchar(60) not null;default:'23:59:59';comment:结束时间" json:"endTime"`
	FixedFee  float64           `gorm:"type:decimal(16,4) not null;default:0;comment:固定手续费" json:"fixedFee"`
	Fee       float64           `gorm:"type:decimal(16,4) not null;default:0;comment:手续费" json:"fee"`
	Level     int8              `gorm:"type:tinyint not null;default:0;comment:等级" json:"level"`
	Status    int8              `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Sort      int16             `gorm:"type:smallint not null;default:99;index;comment:排序" json:"sort"`
	IsProof   int8              `gorm:"type:tinyint not null;default:2;comment:是否需要凭证(1:是,2:否)" json:"isProof"`
	IsChats   int8              `gorm:"type:tinyint not null;default:2;comment:客服(1:真,2:假)" json:"isChats"`
	Data      WalletPaymentData `gorm:"type:json;comment:数据" json:"data"`
	Conf      WalletPaymentConf `gorm:"type:json;comment:配置" json:"conf"`
	Desc      string            `gorm:"type:varchar(255);comment:描述" json:"desc"`
}

// WalletPaymentConf 配置信息
type WalletPaymentConf struct {
	ID          string `json:"id" views:"label:标识符号"`                       //  项目标识符号
	AppKey      string `json:"appKey" views:"label:应用ID"`                   //   应用ID
	AppSecret   string `json:"appSecret" views:"label:应用密钥"`                //	应用密钥
	PaymentType int8   `json:"paymentType" views:"label:支付类型;type:select;"` //	支付类型
	GatewayUrl  string `json:"baseUrl" views:"label:网关地址"`                  //	网关地址
	ReturnUrl   string `json:"returnUrl" views:"label:返回地址"`                //	返回地址
	NotifyUrl   string `json:"notifyUrl" views:"label:通知地址"`                //	通知地址
}

// Value implements the driver.Valuer interface
func (w WalletPaymentConf) Value() (driver.Value, error) {
	return json.Marshal(w)
}

// Scan implements the sql.Scanner interface
func (w *WalletPaymentConf) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &w)
}

// WalletPaymentData 钱包支付额外数据
type WalletPaymentData struct {
	BankName    string `json:"bankName" views:"label:名称｜公链"`    // 银行名称｜公链名称(Ethereum)
	BankAddress string `json:"bankAddress" views:"label:支付地址"`  // 银行地址｜公链标识(Erc20)
	RealName    string `json:"realName" views:"label:姓名|Token"` // 真实姓名｜公链Token(USDT)
	BankCardNo  string `json:"bankCardNo" views:"label:卡号|地址"`  // 银行卡号｜公链地址(0x1234567890abcdef)
	BankCode    string `json:"bankCode" views:"label:代号|简写"`    // 银行代号｜公链简写(ETH)
}

// Value implements the driver.Valuer interface
func (d WalletPaymentData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *WalletPaymentData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// InitWalletPayments initializes the default wallet payments
func InitWalletPayments(db *gorm.DB) error {

	return nil
}

func (p *WalletPayment) BeforeCreate(tx *gorm.DB) error {
	if err := p.Validate(); err != nil {
		return fmt.Errorf("创建验证失败: %v", err)
	}
	return nil
}

func (p *WalletPayment) BeforeUpdate(tx *gorm.DB) error {
	if err := p.Validate(); err != nil {
		return fmt.Errorf("更新验证失败: %v", err)
	}
	return nil
}

func (p *WalletPayment) Validate() error {
	if p.MinAmount <= 0 {
		return fmt.Errorf("最小金额必须大于零")
	}
	if p.MaxAmount < p.MinAmount {
		return fmt.Errorf("最大金额不能小于最小金额")
	}
	if _, err := time.Parse("15:04:05", p.StartTime); err != nil {
		return fmt.Errorf("开始时间格式无效: %v", err)
	}
	if _, err := time.Parse("15:04:05", p.EndTime); err != nil {
		return fmt.Errorf("结束时间格式无效: %v", err)
	}
	return nil
}
