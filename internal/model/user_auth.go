package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	// UserAuthMode constants
	UserAuthModeIdentity int8 = 1 // 身份认证
	UserAuthModeAddress  int8 = 2 // 地址认证

	// UserAuthType constants
	UserAuthTypeIDCard   int8 = 1 // 身份证
	UserAuthTypePassport int8 = 2 // 护照
	UserAuthTypeDriver   int8 = 3 // 驾驶证

	// UserAuthStatus constants
	UserAuthStatusRejected  int8 = -1 // 拒绝
	UserAuthStatusPending   int8 = 10 // 待审核
	UserAuthStatusCompleted int8 = 20 // 已通过
)

// UserAuth 实名认证
type UserAuth struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager  *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	UserID   uint     `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	Mode     int8     `gorm:"type:tinyint not null;default:1;comment:模式(1:身份认证 2:地址认证)" json:"mode"`
	RealName string   `gorm:"type:varchar(50) not null;comment:证件姓名" json:"realName"`
	IDNumber string   `gorm:"type:varchar(50) not null;uniqueIndex;comment:证件号码" json:"idNumber"`
	Photo1   string   `gorm:"type:varchar(255) not null;comment:证件照正面" json:"photo1"`
	Photo2   string   `gorm:"type:varchar(255) not null;comment:证件照反面" json:"photo2"`
	Photo3   string   `gorm:"type:varchar(255);comment:手持证件照" json:"photo3"`
	Address  string   `gorm:"type:varchar(255);comment:详细地址" json:"address"`
	Type     int8     `gorm:"type:tinyint not null;default:1;comment:类型(1:身份证 2:护照 3:驾驶证)" json:"type"`
	Status   int8     `gorm:"type:tinyint not null;default:10;comment:状态(-1:拒绝 10:待审核 20:已通过)" json:"status"`
	Reason   string   `gorm:"type:varchar(255);comment:拒绝原因" json:"reason"`
}

// IsIdentityMode 检查是否为身份认证模式
func (a *UserAuth) IsIdentityMode() bool {
	return a.Mode == UserAuthModeIdentity
}

// IsAddressMode 检查是否为地址认证模式
func (a *UserAuth) IsAddressMode() bool {
	return a.Mode == UserAuthModeAddress
}

// IsIDCardType 检查是否为身份证类型
func (a *UserAuth) IsIDCardType() bool {
	return a.Type == UserAuthTypeIDCard
}

// IsPassportType 检查是否为护照类型
func (a *UserAuth) IsPassportType() bool {
	return a.Type == UserAuthTypePassport
}

// IsDriverType 检查是否为驾驶证类型
func (a *UserAuth) IsDriverType() bool {
	return a.Type == UserAuthTypeDriver
}

// IsRejected 检查认证状态是否为拒绝
func (a *UserAuth) IsRejected() bool {
	return a.Status == UserAuthStatusRejected
}

// IsPending 检查认证状态是否为待审核
func (a *UserAuth) IsPending() bool {
	return a.Status == UserAuthStatusPending
}

// IsCompleted 检查认证状态是否为已通过
func (a *UserAuth) IsCompleted() bool {
	return a.Status == UserAuthStatusCompleted
}

// Validate 验证 UserAuth 结构体的字段
func (a *UserAuth) Validate() error {
	if a.UserID == 0 {
		return errors.New("用户ID不能为空")
	}

	switch a.Mode {
	case UserAuthModeIdentity, UserAuthModeAddress:
		// Valid modes
	default:
		return errors.New("无效的认证模式")
	}

	if a.RealName == "" {
		return errors.New("证件姓名不能为空")
	}

	if a.IDNumber == "" {
		return errors.New("证件号码不能为空")
	}

	switch a.Type {
	case UserAuthTypeIDCard, UserAuthTypePassport, UserAuthTypeDriver:
		// Valid types
	default:
		return errors.New("无效的证件类型")
	}

	switch a.Status {
	case UserAuthStatusRejected, UserAuthStatusPending, UserAuthStatusCompleted:
		// Valid statuses
	default:
		return errors.New("无效的认证状态")
	}

	// 根据模式进行更详细的校验
	if a.Mode == UserAuthModeIdentity {
		if a.Photo1 == "" || a.Photo2 == "" {
			return errors.New("身份认证模式下证件照片不能为空")
		}
	} else if a.Mode == UserAuthModeAddress {
		if a.Address == "" {
			return errors.New("地址认证模式下详细地址不能为空")
		}
	}

	return nil
}

// BeforeCreate GORM 钩子，在创建前调用 Validate
func (a *UserAuth) BeforeCreate(tx *gorm.DB) (err error) {
	return a.Validate()
}

// BeforeUpdate GORM 钩子，在更新前调用 Validate
func (a *UserAuth) BeforeUpdate(tx *gorm.DB) (err error) {
	return a.Validate()
}
