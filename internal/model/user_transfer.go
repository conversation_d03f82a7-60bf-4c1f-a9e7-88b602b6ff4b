package model

import (
	"database/sql/driver"
	"errors"

	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

const (
	// TransferTypeInternal 内部转账
	TransferTypeInternal int8 = 1

	// TransferStatusPending 待处理
	TransferStatusPending int8 = 10
	// TransferStatusProcessing 处理中
	TransferStatusProcessing int8 = 15
	// TransferStatusCompleted 已完成
	TransferStatusCompleted int8 = 20
	// TransferStatusFailed 失败
	TransferStatusFailed int8 = -2
	// TransferStatusRejected 已拒绝
	TransferStatusRejected int8 = -1
	// TransferStatusCancelled 已取消
	TransferStatusCancelled int8 = -3
)

// Transfer 站内转账
type Transfer struct {
	gorm.Model
	MerchantID uint         `gorm:"type:int unsigned not null;index;comment:商户ID" json:"merchantId"`
	TradeNo    string       `gorm:"type:varchar(64) not null;uniqueIndex;comment:交易流水号" json:"tradeNo"` // 新增交易流水号
	SenderID   uint         `gorm:"type:int unsigned not null;index;comment:发送者ID" json:"senderId"`
	ReceiverID uint         `gorm:"type:int unsigned not null;index;comment:接收者ID" json:"receiverId"`
	AssetsID   uint         `gorm:"type:int unsigned not null;index;comment:资产ID" json:"assetsId"`
	Amount     float64      `gorm:"type:decimal(24,8) not null;comment:转账金额" json:"amount"`
	Type       int8         `gorm:"type:tinyint not null;default:1;index;comment:转账类型(1:内部转账)" json:"type"`
	Status     int8         `gorm:"type:tinyint not null;default:10;index;comment:状态(10:待处理, 15:处理中, 20:已完成, -1:已拒绝, -2:失败, -3:已取消)" json:"status"`
	Remark     string       `gorm:"type:varchar(255);comment:备注" json:"remark"`
	Data       TransferData `gorm:"type:json;comment:数据" json:"data"`
}

// TransferData 转账数据 (使用 json.RawMessage 或具体结构体)
// type TransferData json.RawMessage

// Example specific data structure for internal transfer
type InternalTransferData struct {
	SenderAccountSnapshot   string `json:"senderAccountSnapshot"`   // Example: JSON string of sender's account state
	ReceiverAccountSnapshot string `json:"receiverAccountSnapshot"` // Example: JSON string of receiver's account state
	FeeDetails              string `json:"feeDetails"`              // Example: JSON string of fee breakdown
}

// TransferData 可以是一个接口或者使用 json.RawMessage
type TransferData struct {
	// 根据 Type 字段存储不同类型的转账数据
	Internal *InternalTransferData `json:"internal,omitempty"`
	// Other types...
}

// Value implements the driver.Valuer interface
func (d TransferData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *TransferData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// BeforeCreate GORM hook for data validation and pre-processing
func (t *Transfer) BeforeCreate(tx *gorm.DB) (err error) {

	return nil
}

// BeforeUpdate GORM hook to update StatusUpdatedAt when status changes
func (t *Transfer) BeforeUpdate(tx *gorm.DB) (err error) {
	// Check if status has changed (requires querying the old value)
	// A more robust approach might involve checking dirty fields or using a separate status update function
	// For simplicity here, we'll just update the time if the status is explicitly set in the update
	// In a real application, you'd compare t.Status with the original value from the database

	// Example: If you are explicitly updating the status field
	// if tx.Statement.Changed("Status") {
	// 	t.StatusUpdatedAt = time.Now()
	// }

	// A simpler approach if you always want to update the time on any update:
	// t.StatusUpdatedAt = time.Now()

	return nil
}
