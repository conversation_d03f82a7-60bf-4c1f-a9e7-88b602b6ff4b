package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	// TransferTypeInternal 内部转账
	TransferTypeInternal int8 = 1

	// TransferStatusPending 待处理
	TransferStatusPending int8 = 10
	// TransferStatusProcessing 处理中
	TransferStatusProcessing int8 = 15
	// TransferStatusCompleted 已完成
	TransferStatusCompleted int8 = 20
	// TransferStatusFailed 失败
	TransferStatusFailed int8 = -2
	// TransferStatusRejected 已拒绝
	TransferStatusRejected int8 = -1
	// TransferStatusCancelled 已取消
	TransferStatusCancelled int8 = -3
)

// Transfer 站内转账
type Transfer struct {
	gorm.Model
	// 管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager      *Manager `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	TradeNo      string   `gorm:"type:varchar(64) not null;uniqueIndex;comment:交易流水号" json:"tradeNo"`
	SenderID     uint     `gorm:"type:int unsigned not null;index;comment:发送者ID" json:"senderId"`
	ReceiverID   uint     `gorm:"type:int unsigned not null;index;comment:接收者ID" json:"receiverId"`
	AssetsID     uint     `gorm:"type:int unsigned not null;index;comment:资产ID" json:"assetsId"`
	Amount       float64  `gorm:"type:decimal(24,8) not null;comment:转账金额" json:"amount"`
	Fee          float64  `gorm:"type:decimal(24,8);default:0;comment:'手续费'" json:"fee"`
	ActualAmount float64  `gorm:"type:decimal(24,8) not null;comment:'实际到账金额'" json:"actual_amount"`
	Type         int8     `gorm:"type:tinyint not null;default:1;index;comment:转账类型(1:内部转账)" json:"type"`
	Status       int8     `gorm:"type:tinyint not null;default:10;index;comment:状态(10:待处理, 15:处理中, 20:已完成, -1:已拒绝, -2:失败, -3:已取消)" json:"status"`
	Remark       string   `gorm:"type:varchar(255);comment:备注" json:"remark"`
}

// IsInternalTransfer 检查是否为内部转账
func (t *Transfer) IsInternalTransfer() bool {
	return t.Type == TransferTypeInternal
}

// IsPending 检查转账是否处于待处理状态
func (t *Transfer) IsPending() bool {
	return t.Status == TransferStatusPending
}

// IsProcessing 检查转账是否处于处理中状态
func (t *Transfer) IsProcessing() bool {
	return t.Status == TransferStatusProcessing
}

// IsCompleted 检查转账是否已完成
func (t *Transfer) IsCompleted() bool {
	return t.Status == TransferStatusCompleted
}

// IsFailed 检查转账是否失败
func (t *Transfer) IsFailed() bool {
	return t.Status == TransferStatusFailed
}

// IsRejected 检查转账是否被拒绝
func (t *Transfer) IsRejected() bool {
	return t.Status == TransferStatusRejected
}

// IsCancelled 检查转账是否已取消
func (t *Transfer) IsCancelled() bool {
	return t.Status == TransferStatusCancelled
}

// Validate 校验转账数据
func (t *Transfer) Validate() error {
	if t.TradeNo == "" {
		return errors.New("交易流水号不能为空")
	}
	if t.SenderID == 0 {
		return errors.New("发送者ID不能为空")
	}
	if t.ReceiverID == 0 {
		return errors.New("接收者ID不能为空")
	}
	if t.Amount <= 0 {
		return errors.New("转账金额必须大于0")
	}
	if t.ActualAmount <= 0 {
		return errors.New("实际到账金额必须大于0")
	}
	// 可以在这里添加更多关于 Type 和 Status 的校验
	return nil
}

// BeforeCreate GORM hook for data validation and pre-processing
func (t *Transfer) BeforeCreate(tx *gorm.DB) (err error) {
	return t.Validate()
}

// BeforeUpdate GORM hook to update StatusUpdatedAt when status changes
func (t *Transfer) BeforeUpdate(tx *gorm.DB) (err error) {
	// 在更新前也进行校验
	return t.Validate()
}
