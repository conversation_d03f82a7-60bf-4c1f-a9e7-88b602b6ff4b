package model

import (
	"errors"

	"gorm.io/gorm"
)

const (
	// 域名状态
	DomainStatusPending  int = 0 // 待验证
	DomainStatusActive   int = 1 // 已验证/活跃
	DomainStatusDisabled int = 2 // 已禁用

	// 域名类型
	DomainTypePrimary   int = 1 // 主域名
	DomainTypeSecondary int = 2 // 次要域名
	DomainTypeCustom    int = 3 // 自定义域名
)

// MerchantDomain 商户域名
type MerchantDomain struct {
	BaseModel

	// MerchantID 关联的商户ID，作为外键
	MerchantID uint `gorm:"index;not null;comment:'所属商户ID'" json:"merchant"`
	// Merchant 关联的商户对象
	Merchant *Merchant `gorm:"foreignKey:MerchantID" json:"tenant,omitempty"`

	// Domain 域名，唯一索引，确保一个域名只属于一个租户
	Domain string `gorm:"type:varchar(255);uniqueIndex;not null;comment:'域名'" json:"domain"`
	// Remark 域名备注信息
	Remark string `gorm:"type:varchar(255);comment:'备注'" json:"remark"`
	// Type 域名类型：1-主域名，2-次要域名，3-自定义域名
	Type int `gorm:"type:smallint;not null;default:1;comment:'类型'" json:"type"`

	// Status 域名状态：0-待验证，1-已验证/活跃，2-已禁用
	Status int `gorm:"type:smallint;not null;default:1;comment:'状态'" json:"status"`
}

// IsActive 检查域名是否处于活跃状态
func (d *MerchantDomain) IsActive() bool {
	return d.Status == DomainStatusActive
}

// IsPending 检查域名是否待验证
func (d *MerchantDomain) IsPending() bool {
	return d.Status == DomainStatusPending
}

// IsDisabled 检查域名是否被禁用
func (d *MerchantDomain) IsDisabled() bool {
	return d.Status == DomainStatusDisabled
}

// IsPrimary 检查是否是主域名
func (d *MerchantDomain) IsPrimary() bool {
	return d.Type == DomainTypePrimary
}

// IsSecondary 检查是否是次要域名
func (d *MerchantDomain) IsSecondary() bool {
	return d.Type == DomainTypeSecondary
}

// IsCustom 检查是否是自定义域名
func (d *MerchantDomain) IsCustom() bool {
	return d.Type == DomainTypeCustom
}

// Validate 验证域名数据
func (d *MerchantDomain) Validate() error {
	if d.Domain == "" {
		return errors.New("domain is required")
	}
	if len(d.Domain) > 255 {
		return errors.New("domain length must be less than 255")
	}
	if d.MerchantID == 0 {
		return errors.New("merchant_id is required")
	}
	if d.Status < DomainStatusPending || d.Status > DomainStatusDisabled {
		return errors.New("invalid status")
	}
	if d.Type < DomainTypePrimary || d.Type > DomainTypeCustom {
		return errors.New("invalid type")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (d *MerchantDomain) BeforeCreate(tx *gorm.DB) error {
	return d.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (d *MerchantDomain) BeforeUpdate(tx *gorm.DB) error {
	return d.Validate()
}

// GetMerchant 获取关联的商户
func (d *MerchantDomain) GetMerchant() *Merchant {
	return d.Merchant
}

// SetMerchant 设置关联的商户
func (d *MerchantDomain) SetMerchant(merchant *Merchant) {
	d.Merchant = merchant
	d.MerchantID = merchant.ID
}

// Activate 激活域名
func (d *MerchantDomain) Activate() {
	d.Status = DomainStatusActive
}

// Disable 禁用域名
func (d *MerchantDomain) Disable() {
	d.Status = DomainStatusDisabled
}

// SetPending 设置为待验证状态
func (d *MerchantDomain) SetPending() {
	d.Status = DomainStatusPending
}

// SetPrimary 设置为主域名
func (d *MerchantDomain) SetPrimary() {
	d.Type = DomainTypePrimary
}

// SetSecondary 设置为次要域名
func (d *MerchantDomain) SetSecondary() {
	d.Type = DomainTypeSecondary
}

// SetCustom 设置为自定义域名
func (d *MerchantDomain) SetCustom() {
	d.Type = DomainTypeCustom
}
