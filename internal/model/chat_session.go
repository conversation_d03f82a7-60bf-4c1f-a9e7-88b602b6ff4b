package model

import (
	"database/sql/driver"
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/goccy/go-json"
)

const (
	ChatsSessionsTypeUser = 1 // 用户

	ChatsSessionsStatusPending = 10 // 等待
	ChatsSessionsStatusActive  = 20 // 活跃
	ChatsSessionsStatusClosed  = 30 // 结束
)

// ChatsSessions 聊天会话
type ChatsSessions struct {
	gorm.Model
	// 操作的管理员ID
	ManagerID uint `gorm:"index;not null;comment:'管理员ID'" json:"manager_id"`
	// 关联的管理员
	Manager   *Manager          `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	SessionID string            `gorm:"type:varchar(255);uniqueIndex;comment:会话ID" json:"sessionId"`
	UserID    uint              `gorm:"type:int unsigned;index;comment:用户ID" json:"userId"`
	Name      string            `gorm:"type:varchar(255);comment:名称" json:"name"`
	Type      int8              `gorm:"type:tinyint;default:1;comment:类型(1:用户)" json:"type"`
	Number    int8              `gorm:"type:smallint;default:0;comment:未读消息" json:"number"`
	Status    int8              `gorm:"type:tinyint;default:10;comment:状态(10:等待,20:活跃,30:结束)" json:"status"`
	IsPinned  bool              `gorm:"default:false;comment:是否置顶" json:"isPinned"`
	Data      ChatsSessionsData `gorm:"type:json;comment:数据" json:"data"`
}

// ChatsSessionsData 聊天会话数据
type ChatsSessionsData struct {
	ID         uint      `json:"id"`         //	最后消息ID
	SessionID  string    `json:"sessionId"`  //	最后消息会话ID
	SenderID   uint      `json:"senderId"`   //	最后消息发送者ID
	SenderType int8      `json:"senderType"` //	最后消息发送者类型
	ReceiverID uint      `json:"receiverId"` //	最后消息接收者ID
	Type       int8      `json:"type"`       //	最后消息类型
	Message    string    `json:"message"`    //	最后消息内容
	Status     int8      `json:"status"`     //	最后消息状态
	CreatedAt  time.Time `json:"createdAt"`  //	最后消息时间
}

// Value implements the driver.Valuer interface
func (d ChatsSessionsData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *ChatsSessionsData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// Validate 校验 ChatsSessions 数据
func (cs *ChatsSessions) Validate() error {
	if cs.ManagerID == 0 {
		return errors.New("manager ID is required")
	}
	if cs.SessionID == "" {
		return errors.New("session ID is required")
	}
	if cs.UserID == 0 {
		return errors.New("user ID is required")
	}
	if cs.Name == "" {
		return errors.New("name is required")
	}
	// 校验 Type 是否在有效范围内
	if cs.Type != ChatsSessionsTypeUser {
		return errors.New("invalid session type")
	}
	// 校验 Status 是否在有效范围内
	if cs.Status != ChatsSessionsStatusPending && cs.Status != ChatsSessionsStatusActive && cs.Status != ChatsSessionsStatusClosed {
		return errors.New("invalid session status")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (cs *ChatsSessions) BeforeCreate(tx *gorm.DB) error {
	return cs.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (cs *ChatsSessions) BeforeUpdate(tx *gorm.DB) error {
	return cs.Validate()
}

// IsTypeUser 检查会话是否是用户类型
func (cs *ChatsSessions) IsTypeUser() bool {
	return cs.Type == ChatsSessionsTypeUser
}

// IsStatusPending 检查会话是否处于等待状态
func (cs *ChatsSessions) IsStatusPending() bool {
	return cs.Status == ChatsSessionsStatusPending
}

// IsStatusActive 检查会话是否处于活跃状态
func (cs *ChatsSessions) IsStatusActive() bool {
	return cs.Status == ChatsSessionsStatusActive
}

// IsStatusClosed 检查会话是否处于结束状态
func (cs *ChatsSessions) IsStatusClosed() bool {
	return cs.Status == ChatsSessionsStatusClosed
}

// GetSessionData 获取会话数据
func (cs *ChatsSessions) GetSessionData() *ChatsSessionsData {
	return &cs.Data
}
