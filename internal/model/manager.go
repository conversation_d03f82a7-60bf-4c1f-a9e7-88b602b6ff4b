package model

import (
	"app/pkg/logger"
	"crypto/rand"
	"encoding/base32"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/pquerna/otp/totp"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

const (
	initialSuperManagerUsername = "superadmin"
	initialSuperManagerPassword = "Aa123098.."

	ManagerStatusDisabled int = 0 // 禁用
	ManagerStatusActive   int = 1 // 正常
	ManagerStatusLocked   int = 2 // 锁定
	ManagerStatusPending  int = 3 // 待激活/待审批

	// 管理员层级
	ManagerLevelSuper    int = 1 // 超级管理员
	ManagerLevelMerchant int = 2 // 商户管理员
	ManagerLevelAgent    int = 3 // 代理管理员
)

// Manager 管理员/代理/租户管理员
type Manager struct {
	BaseModel

	// 用户名，唯一标识
	Username string `gorm:"type:varchar(100);uniqueIndex;not null;comment:'用户名'" json:"username"`
	// 邮箱地址，可选
	Email string `gorm:"type:varchar(100);uniqueIndex;comment:'邮箱'" json:"email,omitempty"`
	// 手机号码，可选
	PhoneNumber string `gorm:"type:varchar(20);uniqueIndex;comment:'手机号'" json:"phone_number,omitempty"`
	// 密码哈希值，不返回给前端
	PasswordHash string `gorm:"type:varchar(255);not null;comment:'密码哈希'" json:"-"`

	// 昵称，可选
	Nickname string `gorm:"type:varchar(100);comment:'昵称'" json:"nickname,omitempty"`
	// 头像URL地址，可选
	AvatarURL string `gorm:"type:varchar(255);comment:'头像URL'" json:"avatar_url,omitempty"`

	// 账户状态：0-禁用，1-正常，2-锁定，3-待激活
	Status int `gorm:"type:smallint;not null;default:1;comment:'账户状态'" json:"status"`
	// 账户锁定截止时间
	LockedUntil *time.Time `gorm:"comment:'锁定截止时间'" json:"locked_until,omitempty"`
	// 登录失败次数
	FailedLoginAttempts int `gorm:"type:smallint;not null;default:0;comment:'登录失败次数'" json:"failed_login_attempts"`
	// 最后登录时间
	LastLoginAt *time.Time `gorm:"comment:'最后登录时间'" json:"last_login_at,omitempty"`
	// 最后登录IP地址
	LastLoginIP string `gorm:"type:varchar(45);comment:'最后登录IP'" json:"last_login_ip,omitempty"`
	// 最后密码修改时间
	PasswordChangedAt *time.Time `gorm:"comment:'最后密码修改时间'" json:"password_changed_at,omitempty"`
	// 是否为超级管理员
	IsSuperAdmin bool `gorm:"type:boolean;not null;default:false;comment:'是否是超级管理员'" json:"is_super_admin"`

	// 创建者用户ID
	CreatedByUserID *uint `gorm:"index;comment:'创建者用户ID'" json:"created_by_user_id,omitempty"`
	// 更新者用户ID
	UpdatedByUserID *uint `gorm:"index;comment:'更新者用户ID'" json:"updated_by_user_id,omitempty"`

	// 管理员层级：1-超级管理员，2-商户管理员，3-代理管理员
	ManagerLevel int `gorm:"type:tinyint;not null;default:1;comment:'管理员层级(1超管)'" json:"admin_level"`

	// 上级管理员ID
	ManagerID *uint `gorm:"index;comment:'上级管理员ID'" json:"manager_id,omitempty"`
	// 上级管理员关联
	Manager *Manager `gorm:"foreignKey:ManagerID;references:ID"`
	// 下级管理员列表
	Subordinates []Manager `gorm:"foreignKey:ManagerID"`

	// 管理金额/业绩/余额
	ManagedAmount float64 `gorm:"type:decimal(18,2);comment:'管理金额/业绩/余额'" json:"managed_amount"`

	// 是否启用MFA双因素认证
	IsMFAEnabled bool `gorm:"type:boolean;not null;default:false;comment:'是否启用MFA'" json:"is_mfa_enabled"`
	// MFA密钥哈希或加密值
	MFASecret string `gorm:"type:varchar(255);comment:'MFA密钥哈希或加密值'" json:"-"`
	// MFA恢复码哈希
	MFARecoveryCodesHash string `gorm:"type:varchar(255);comment:'MFA恢复码哈希'" json:"-"`
	// 最后MFA验证时间
	LastMFAValidatedAt *time.Time `gorm:"comment:'最后MFA验证时间'" json:"last_mfa_validated_at,omitempty"`
}

// IsActive 检查管理员是否处于活跃状态
func (m *Manager) IsActive() bool {
	return m.Status == ManagerStatusActive
}

// IsDisabled 检查管理员是否被禁用
func (m *Manager) IsDisabled() bool {
	return m.Status == ManagerStatusDisabled
}

// IsLocked 检查管理员是否被锁定
func (m *Manager) IsLocked() bool {
	return m.Status == ManagerStatusLocked
}

// IsPending 检查管理员是否待激活
func (m *Manager) IsPending() bool {
	return m.Status == ManagerStatusPending
}

// IsSuperManager 检查是否是超级管理员
func (m *Manager) IsSuperManager() bool {
	return m.ManagerLevel == ManagerLevelSuper
}

// IsMerchantManager 检查是否是商户管理员
func (m *Manager) IsMerchantManager() bool {
	return m.ManagerLevel == ManagerLevelMerchant
}

// IsAgentManager 检查是否是代理管理员
func (m *Manager) IsAgentManager() bool {
	return m.ManagerLevel == ManagerLevelAgent
}

// Validate 验证管理员数据
func (m *Manager) Validate() error {
	if m.Username == "" {
		return errors.New("username is required")
	}
	if len(m.Username) < 3 || len(m.Username) > 100 {
		return errors.New("username length must be between 3 and 100")
	}
	if m.Email != "" {
		// TODO: 添加邮箱格式验证
	}
	if m.PhoneNumber != "" {
		// TODO: 添加手机号格式验证
	}
	if m.ManagerLevel < ManagerLevelSuper || m.ManagerLevel > ManagerLevelAgent {
		return errors.New("invalid manager level")
	}
	return nil
}

// BeforeCreate GORM 钩子：创建前验证
func (m *Manager) BeforeCreate(tx *gorm.DB) error {
	return m.Validate()
}

// BeforeUpdate GORM 钩子：更新前验证
func (m *Manager) BeforeUpdate(tx *gorm.DB) error {
	return m.Validate()
}

// InitManager 初始化管理用户
func InitManager(db *gorm.DB, appLogger logger.Logger) error {
	// 自动迁移 AdminUser 表
	if err := db.AutoMigrate(&Manager{}); err != nil {
		return fmt.Errorf("failed to auto migrate Manager table in init: %v", err)
	}

	// Count 方法更适合只检查是否存在记录，而不是取出一条记录
	var count int64
	result := db.Model(&Manager{}).Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check Manager count in init: %v", result.Error)
	}

	// 如果没有数据, 那么创建超级管理
	if count == 0 {
		// 哈希初始化密码
		hashedPassword, hashErr := bcrypt.GenerateFromPassword([]byte(initialSuperManagerPassword), bcrypt.DefaultCost)
		if hashErr != nil {
			return fmt.Errorf("failed to hash initial super admin password: %v", hashErr)
		}

		superManager := &Manager{
			Username:     initialSuperManagerUsername,
			PasswordHash: string(hashedPassword),
			Status:       ManagerStatusActive, // 设置为激活状态
			IsSuperAdmin: true,                // 设置为超级管理员
			ManagerLevel: ManagerLevelSuper,   // 设置超级管理员等级
		}
		superManager.GenerateGoogleAuthSecret()
		// 保存超级管理员用户到数据库
		createResult := db.Create(superManager)
		if createResult.Error != nil {
			return fmt.Errorf("failed to create initial super administrator in init: %v", createResult.Error)
		}

		appLogger.Info("Initial super administrator created successfully.")
	}

	return nil
}

// VerifyGoogleAuthCode 验证 Google Authenticator 验证码
func (m *Manager) VerifyGoogleAuthCode(code string) bool {
	if !m.IsMFAEnabled || m.MFASecret == "" {
		return false
	}

	// 使用 Google Authenticator 库验证验证码
	valid, err := totp.ValidateCustom(code, m.MFASecret, time.Now(), totp.ValidateOpts{
		Period: 30, // 30秒有效期
		Skew:   1,  // 允许前后1个时间窗口
		Digits: 6,  // 6位数字
	})

	if err != nil {
		return false
	}

	return valid
}

// GenerateGoogleAuthSecret 生成 Google Authenticator 密钥
func (m *Manager) GenerateGoogleAuthSecret() (string, error) {
	// 生成密钥
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      "Admin System", // 发行者名称
		AccountName: m.Username,     // 账户名称
		SecretSize:  20,             // 密钥长度
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate Google Authenticator secret: %v", err)
	}

	// 保存密钥
	m.MFASecret = key.Secret()
	m.IsMFAEnabled = true
	m.LastMFAValidatedAt = nil // 重置最后验证时间

	return key.Secret(), nil
}

// GetGoogleAuthQRCode 获取 Google Authenticator 二维码内容
func (m *Manager) GetGoogleAuthQRCode() (string, error) {
	if m.MFASecret == "" {
		return "", errors.New("MFA secret not set")
	}

	// 生成二维码内容
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      "Admin System",
		AccountName: m.Username,
		Secret:      []byte(m.MFASecret),
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate QR code: %v", err)
	}

	return key.Secret(), nil
}

// GenerateMFARecoveryCodes 生成 MFA 恢复码
func (m *Manager) GenerateMFARecoveryCodes(count int) ([]string, error) {
	if count <= 0 || count > 10 {
		return nil, errors.New("recovery codes count must be between 1 and 10")
	}

	// 生成恢复码
	codes := make([]string, count)
	for i := 0; i < count; i++ {
		// 生成 16 字节的随机数
		randomBytes := make([]byte, 16)
		if _, err := rand.Read(randomBytes); err != nil {
			return nil, fmt.Errorf("failed to generate recovery code: %v", err)
		}
		// 使用 base32 编码，并移除填充字符
		code := strings.TrimRight(base32.StdEncoding.EncodeToString(randomBytes), "=")
		// 每 4 个字符添加一个连字符，使其更易读
		formattedCode := ""
		for j := 0; j < len(code); j += 4 {
			if j > 0 {
				formattedCode += "-"
			}
			end := j + 4
			if end > len(code) {
				end = len(code)
			}
			formattedCode += code[j:end]
		}
		codes[i] = formattedCode
	}

	// 将所有恢复码连接成一个字符串，用换行符分隔
	allCodes := strings.Join(codes, "\n")

	// 对恢复码进行哈希处理
	hashedCodes, err := bcrypt.GenerateFromPassword([]byte(allCodes), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash recovery codes: %v", err)
	}

	// 保存哈希值
	m.MFARecoveryCodesHash = string(hashedCodes)

	return codes, nil
}

// VerifyMFARecoveryCode 验证 MFA 恢复码
func (m *Manager) VerifyMFARecoveryCode(code string) bool {
	if m.MFARecoveryCodesHash == "" {
		return false
	}

	// 验证恢复码
	err := bcrypt.CompareHashAndPassword([]byte(m.MFARecoveryCodesHash), []byte(code))
	if err != nil {
		return false
	}

	// 验证成功后，清除恢复码哈希（一次性使用）
	m.MFARecoveryCodesHash = ""
	return true
}
