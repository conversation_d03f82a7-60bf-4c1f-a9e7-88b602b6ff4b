package manager

import (
	"app/internal/dto/common"
	"time"
)

// ManagerLogListQuery 管理员日志查询参数
type ManagerLogListQuery struct {
	// ManagerID 管理员ID
	ManagerID *int64 `json:"manager_id" form:"manager_id" binding:"omitempty"`
	// Type 操作类型
	Type *int `json:"type" form:"type" binding:"omitempty"`
	// Module 操作模块
	Module *string `json:"module" form:"module" binding:"omitempty"`
	// IP 操作IP地址
	IP *string `json:"ip" form:"ip" binding:"omitempty"`
	// StartTime 开始时间
	StartTime *string `json:"start_time" form:"start_time" binding:"omitempty"`
	// EndTime 结束时间
	EndTime *string `json:"end_time" form:"end_time" binding:"omitempty"`
	// Method 请求方法
	Method *string `json:"method" form:"method" binding:"omitempty"`
	// Path 请求路径
	Path *string `json:"path" form:"path" binding:"omitempty"`
	// Params 请求参数
	Params *string `json:"params" form:"params" binding:"omitempty"`

	// Pagination 分页参数
	common.PageRequest
}

// ManagerLogCreateQuery 管理员日志创建请求
type ManagerLogCreateQuery struct {
	// ManagerID 管理员ID
	ManagerID int64 `json:"manager_id" binding:"required"`
	// Type 操作类型
	Type int `json:"type" binding:"required"`
	// Module 操作模块
	Module string `json:"module" binding:"required"`
	// Content 操作内容
	Content string `json:"content" binding:"required"`
	// Result 操作结果
	Result int `json:"result" binding:"required"`
	// Error 错误信息
	Error string `json:"error" binding:"omitempty"`
	// IP 操作IP地址
	IP string `json:"ip" binding:"required"`
	// OperatedAt 操作时间
	OperatedAt time.Time `json:"operated_at" binding:"required"`
	// Method 请求方法
	Method string `json:"method" binding:"required"`
	// Path 请求路径
	Path string `json:"path" binding:"required"`
	// Params 请求参数
	Params string `json:"params" binding:"required"`
	// Duration 操作耗时（毫秒）
	Duration int64 `json:"duration" binding:"required"`
	// UserAgent 用户代理
	UserAgent string `json:"user_agent" binding:"required"`
}
