package manager

import "app/internal/dto/common"

// ManagerMenuListQuery 分页管理员菜单查询
type ManagerMenuListQuery struct {
	// 菜单ID
	ID *uint `json:"id" form:"id" binding:"omitempty"`
	// 菜单名称
	Name *string `json:"name" form:"name" binding:"omitempty"`
	// 菜单标题
	Title *string `json:"title" form:"title" binding:"omitempty"`
	// 菜单类型
	Type *int `json:"type" form:"type" binding:"omitempty"`
	// 父级菜单ID
	ParentID *uint `json:"parent_id" form:"parent_id" binding:"omitempty"`
	// 菜单状态
	Status *int `json:"status" form:"status" binding:"omitempty"`
	// 是否外部链接
	Frame *int `json:"frame" form:"frame" binding:"omitempty"`
	// 是否缓存
	Cache *int `json:"cache" form:"cache" binding:"omitempty"`
	// 是否可见
	Visible *int `json:"visible" form:"visible" binding:"omitempty"`
	// 菜单路径
	Path *string `json:"path" form:"path" binding:"omitempty"`
	// Pagination 分页参数
	common.PageRequest
}

// ManagerMenuUpdateQuery 管理员菜单更新数据
type ManagerMenuUpdateQuery struct {
	// 菜单ID
	ID uint `json:"id" binding:"required"`
	// 菜单名称
	Name string `json:"name" binding:"required,max=50"`
	// 菜单标题
	Title string `json:"title" binding:"required,max=50"`
	// 父级菜单ID
	ParentID uint `json:"parent_id" binding:"required"`
	// 菜单路径
	Path string `json:"path" binding:"required,max=200"`
	// 权限标识
	Permission string `json:"permission" binding:"max=100"`
	// 菜单状态
	Status int `json:"status" binding:"required,oneof=0 1"`
	// 是否外部链接
	Frame int `json:"frame" binding:"required,oneof=0 1"`
	// 组件路径
	Component string `json:"component" binding:"max=200"`
	// 是否缓存
	Cache int `json:"cache" binding:"required,oneof=0 1"`
	// 菜单类型
	Type int `json:"type" binding:"required,oneof=1 2 3"`
	// 是否可见
	Visible int `json:"visible" binding:"required,oneof=0 1"`
	// 排序
	Sort int `json:"sort" binding:"required,min=0"`
	// 图标
	Icon string `json:"icon" binding:"max=100"`
	// 备注
	Remark string `json:"remark" binding:"max=500"`
}
