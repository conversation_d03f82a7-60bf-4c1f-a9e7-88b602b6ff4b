// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package manager

import (
	"app/internal/config"
	"app/internal/handler/adm/manager"
	"app/internal/repository"
	"app/internal/service"
	"app/pkg/cache"
	"app/pkg/logger"
	"app/pkg/validator"
	"gorm.io/gorm"
)

// Injectors from manager_log_wire.go:

// ManagerLogHandler创建管理员日志服务依赖
func ManagerLogHandler(cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator2 *validator.Validator) *manager.ManagerLogHandler {
	managerLogRepo := repository.NewManagerLogRepoImpl(db, rds)
	managerLogService := service.NewManagerLogServiceImpl(appLogger, managerLogRepo)
	managerLogHandler := manager.NewManagerLogHandler(cfg, validator2, managerLogService)
	return managerLogHandler
}

// Injectors from manager_menu_wire.go:

// ManagerMenuHandler 创建管理员菜单
func ManagerMenuHandler(cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator2 *validator.Validator) *manager.ManagerMenuHandler {
	managerMenuRepo := repository.NewManagerMenuRepoImpl(db, rds)
	managerMenuService := service.NewManagerMenuServiceImpl(appLogger, managerMenuRepo)
	managerMenuHandler := manager.NewManagerMenuHandler(cfg, validator2, managerMenuService)
	return managerMenuHandler
}

// Injectors from manager_wire.go:

// ManagerHandler 创建管理员处理器
func ManagerHandler(cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator2 *validator.Validator) *manager.ManagerHandler {
	managerRepo := repository.NewManagerRepoImpl(db, rds)
	managerService := service.NewManagerServiceImpl(appLogger, managerRepo)
	managerHandler := manager.NewManagerHandler(cfg, validator2, managerService)
	return managerHandler
}

// Injectors from merchant_wire.go:

// MerchantHandler创建商户服务依赖
func MerchantHandler(cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator2 *validator.Validator) *manager.MerchantHandler {
	merchantRepo := repository.NewMerchantRepoImpl(db, rds)
	merchantService := service.NewMerchantServiceImpl(appLogger, merchantRepo)
	merchantHandler := manager.NewMerchantHandler(cfg, validator2, merchantService)
	return merchantHandler
}
