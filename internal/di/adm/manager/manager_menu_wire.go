//go:build wireinject
// +build wireinject

package manager

import (
	"app/internal/config"
	"app/internal/handler/adm/manager"
	"app/internal/repository"
	"app/internal/service"
	"app/pkg/cache"
	"app/pkg/logger"
	"app/pkg/validator"

	"github.com/google/wire"
	"gorm.io/gorm"
)

// ManagerMenuHandler 创建管理员菜单
func ManagerMenuHandler(cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator *validator.Validator) *manager.ManagerMenuHandler {
	wire.Build(
		repository.NewManagerMenuRepoImpl,
		service.NewManagerMenuServiceImpl,
		manager.NewManagerMenuHandler,
	)
	return nil
}
