//go:build wireinject
// +build wireinject

package manager

import (
	"app/internal/config"
	"app/internal/handler/adm/manager"
	"app/internal/repository"
	"app/internal/service"
	"app/pkg/cache"
	"app/pkg/logger"
	"app/pkg/validator"

	"github.com/google/wire"
	"gorm.io/gorm"
)

// ManagerLogHandler创建管理员日志服务依赖
func ManagerLogHandler(cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator *validator.Validator) *manager.ManagerLogHandler {
	wire.Build(
		repository.NewManagerLogRepoImpl,
		service.NewManagerLogServiceImpl,
		manager.NewManagerLogHandler,
	)
	return nil
}
