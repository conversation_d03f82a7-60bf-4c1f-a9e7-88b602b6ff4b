package handler

import (
	"app/internal/config"
	"app/internal/di/adm/manager"
	"app/pkg/cache"
	"app/pkg/logger"
	"app/pkg/validator"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ManagerRouters 管理员路由
type ManagerRouters struct {
	db        *gorm.DB
	app       *gin.Engine
	cfg       *config.Config
	rds       *cache.RedisService
	validator *validator.Validator
	appLogger logger.Logger
	router    *gin.RouterGroup
}

// NewManagerRouters 创建管理员路由
func NewManagerRouters(app *gin.Engine, cfg *config.Config, db *gorm.DB, rds *cache.RedisService, appLogger logger.Logger, validator *validator.Validator) *ManagerRouters {
	return &ManagerRouters{
		app:       app,
		cfg:       cfg,
		validator: validator,
		db:        db,
		rds:       rds,
		appLogger: appLogger,
		router:    app.Group("/managers"),
	}
}

// Register 注册管理员路由
func (h *ManagerRouters) Register() {
	// 注册管理员路由
	manager.ManagerHandler(h.cfg, h.db, h.rds, h.appLogger, h.validator).Register(h.router)
	// 注册商户路由
	manager.MerchantHandler(h.cfg, h.db, h.rds, h.appLogger, h.validator).Register(h.router)
	// 注册菜单路由
	manager.ManagerMenuHandler(h.cfg, h.db, h.rds, h.appLogger, h.validator).Register(h.router)
}
