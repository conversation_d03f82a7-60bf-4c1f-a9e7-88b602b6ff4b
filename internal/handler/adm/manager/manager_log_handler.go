// Package manager 提供管理员相关的HTTP处理程序
package manager

import (
	"app/internal/config"
	"app/internal/dto/adm/manager"
	"app/internal/dto/common"
	"app/internal/middleware"
	"app/internal/service"
	"app/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ManagerLogHandler 处理管理员日志相关的HTTP请求
type ManagerLogHandler struct {
	cfg               *config.Config            // 应用配置
	validator         *validator.Validator      // 验证器
	managerLogService service.ManagerLogService // 管理员日志服务
}

// NewManagerLogHandler 创建一个新的ManagerLogHandler实例
func NewManagerLogHandler(cfg *config.Config, validator *validator.Validator, managerLogService service.ManagerLogService) *ManagerLogHandler {
	return &ManagerLogHandler{
		cfg:               cfg,
		validator:         validator,
		managerLogService: managerLogService,
	}
}

// Register 注册管理员日志相关的路由
func (mlh *ManagerLogHandler) Register(r *gin.RouterGroup) {
	managerLogs := r.Group("/logs")
	auth := managerLogs.Group("")
	auth.Use(middleware.JWTAuth(&mlh.cfg.JWT))
	{
		auth.GET("", mlh.List)          // 获取管理员日志列表
		auth.DELETE("/:id", mlh.Delete) // 删除指定ID的管理员日志
	}
}

// Index 处理获取管理员日志列表的请求
// 支持分页和查询参数
func (mlh *ManagerLogHandler) List(c *gin.Context) {
	query := &manager.ManagerLogListQuery{}

	if err := c.ShouldBindQuery(query); err != nil {
		common.BadRequest(c, mlh.validator.HandlerTranslate(c, err))
		return
	}

	result, err := mlh.managerLogService.List(c.Request.Context(), query)
	if err != nil {
		common.Error(c, err.Error())
		return
	}

	common.Success(c, result)
}

// Delete 处理删除管理员日志的请求
// 通过URL参数接收要删除的日志ID
func (mlh *ManagerLogHandler) Delete(c *gin.Context) {
	idParam := common.StrconvStrToUint(c.Param("id"))
	if err := mlh.managerLogService.Delete(c.Request.Context(), idParam); err != nil {
		common.Error(c, err.Error())
		return
	}

	common.Success(c, "ok")
}

// GetManagerLogService 获取管理员日志服务
func (mlh *ManagerLogHandler) GetManagerLogService() service.ManagerLogService {
	return mlh.managerLogService
}
