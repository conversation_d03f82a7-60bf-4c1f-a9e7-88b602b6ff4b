package manager

import (
	"app/internal/config"
	"app/internal/dto/adm/manager"
	"app/internal/dto/common"
	"app/internal/service"
	"app/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ManagerMenuHandler 管理员菜单
type ManagerMenuHandler struct {
	cfg                *config.Config
	validator          *validator.Validator
	managerMenuService service.ManagerMenuService
}

// NewManagerMenuHandler 创建管理员菜单
func NewManagerMenuHandler(cfg *config.Config, validator *validator.Validator, managerMenuService service.ManagerMenuService) *ManagerMenuHandler {
	return &ManagerMenuHandler{
		cfg:                cfg,
		validator:          validator,
		managerMenuService: managerMenuService,
	}
}

// Register 注册菜单路由
func (mc *ManagerMenuHandler) Register(r *gin.RouterGroup) {
	managerMenus := r.Group("/menu")
	{
		managerMenus.GET("", mc.List)       // 查询列表
		managerMenus.GET("/:id", mc.Info)   // 查询信息
		managerMenus.PUT("/:id", mc.Update) // 更新菜单
	}
}

// List 菜单列表
func (mc *ManagerMenuHandler) List(c *gin.Context) {
	query := &manager.ManagerMenuListQuery{}
	if err := c.ShouldBindQuery(query); err != nil {
		common.BadRequest(c, mc.validator.HandlerTranslate(c, err))
		return
	}
	// 调用服务层方法
	result, err := mc.managerMenuService.List(c.Request.Context(), query)
	if err != nil {
		common.Error(c, err.Error())
		return
	}
	common.Success(c, result)
}

// Info 菜单详情
func (mc *ManagerMenuHandler) Info(c *gin.Context) {
	idParam := common.StrconvStrToUint(c.Param("id"))
	menuInfo, err := mc.managerMenuService.GetByID(c.Request.Context(), idParam)
	if err != nil {
		common.Error(c, err.Error())
		return
	}
	common.Success(c, menuInfo)
}

// Update 更新菜单
func (mc *ManagerMenuHandler) Update(c *gin.Context) {
	var bodyParams manager.ManagerMenuUpdateQuery
	if err := c.ShouldBindJSON(&bodyParams); err != nil {
		common.BadRequest(c, err.Error())
		return
	}
	idParam := common.StrconvStrToUint(c.Param("id"))
	if err := mc.managerMenuService.Update(c.Request.Context(), idParam, &bodyParams); err != nil {
		common.Error(c, err.Error())
		return
	}
	common.Success(c, "ok")
}
