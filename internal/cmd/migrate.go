package cmd

import (
	"app/internal/config"
	"app/internal/model"
	"app/pkg/logger"

	adapter "github.com/casbin/gorm-adapter/v3"

	"gorm.io/gorm"
)

// Migrate 运行数据库迁移
func Migrate(cfg *config.Config, db *gorm.DB, appLogger logger.Logger) error {
	var err error

	// 初始化权限表
	_, err = adapter.NewAdapterByDB(db)
	if err != nil {
		return err
	}

	// 初始化管理表
	err = model.InitManager(db, appLogger)
	if err != nil {
		return err
	}

	// 初始化管理日志表
	err = model.InitManagerLog(db, appLogger)
	if err != nil {
		return err
	}

	// 初始化管理菜单表
	err = model.InitManagerMenu(db, appLogger)
	if err != nil {
		return err
	}

	// 初始化商户表
	err = model.InitMerchant(db, appLogger)
	if err != nil {
		return err
	}

	// 初始化管理员通知表
	err = model.InitManagerNotice(db, appLogger)
	if err != nil {
		return err
	}

	return err
}
