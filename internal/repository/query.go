package repository

import (
	"fmt"
	"reflect"
	"strings"

	"gorm.io/gorm"
)

// Operator 查询操作符
type Operator string

// SortDirection 排序方向
type SortDirection string

const (
	OperatorEQ      Operator = "="
	OperatorLIKE    Operator = "LIKE"
	OperatorIN      Operator = "IN"
	OperatorGT      Operator = ">"
	OperatorGTE     Operator = ">="
	OperatorLT      Operator = "<"
	OperatorLTE     Operator = "<="
	OperatorBETWEEN Operator = "BETWEEN"

	SortASC  SortDirection = "ASC"
	SortDESC SortDirection = "DESC"
)

// Sort 排序条件
type Sort struct {
	Field     string
	Direction SortDirection
}

// Condition 查询条件
type Condition struct {
	Field    string
	Operator Operator
	Value    interface{}
}

// Pagination 分页参数
type Pagination struct {
	Page     int
	PageSize int
	Enabled  bool
}

// NewPagination 创建分页参数
func NewPagination() *Pagination {
	return &Pagination{
		Page:     1,
		PageSize: 10,
		Enabled:  false,
	}
}

// SetPage 设置页码
func (p *Pagination) SetPage(page int) *Pagination {
	if page > 0 {
		p.Page = page
		p.Enabled = true
	}
	return p
}

// SetPageSize 设置每页数量
func (p *Pagination) SetPageSize(pageSize int) *Pagination {
	if pageSize > 0 {
		p.PageSize = pageSize
		p.Enabled = true
	}
	return p
}

// QueryBuilder 通用查询构建器
type QueryBuilder struct {
	conditions []Condition
	sorts      []Sort
	groups     []string
	preloads   []string
	selects    []string
	joins      []string
	Pagination *Pagination
}

// NewQueryBuilder 创建新的查询构建器
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		conditions: make([]Condition, 0),
		sorts:      make([]Sort, 0),
		groups:     make([]string, 0),
		preloads:   make([]string, 0),
		selects:    make([]string, 0),
		joins:      make([]string, 0),
		Pagination: NewPagination(),
	}
}

// AddCondition 添加查询条件
func (qb *QueryBuilder) AddCondition(field string, value interface{}) *QueryBuilder {
	return qb.AddConditionWithOperator(field, OperatorEQ, value)
}

// AddConditionWithOperator 添加带操作符的查询条件
func (qb *QueryBuilder) AddConditionWithOperator(field string, operator Operator, value interface{}) *QueryBuilder {
	if value != nil {
		// 如果是指针类型，获取其实际值
		if reflect.TypeOf(value).Kind() == reflect.Ptr {
			// 如果指针为nil，不添加条件
			if reflect.ValueOf(value).IsNil() {
				return qb
			}
			// 获取指针指向的值
			value = reflect.ValueOf(value).Elem().Interface()
		}
		qb.conditions = append(qb.conditions, Condition{
			Field:    field,
			Operator: operator,
			Value:    value,
		})
	}
	return qb
}

// AddLikeCondition 添加LIKE查询条件
func (qb *QueryBuilder) AddLikeCondition(field string, value interface{}) *QueryBuilder {
	if value != nil {
		// 如果是指针类型，获取其实际值
		if reflect.TypeOf(value).Kind() == reflect.Ptr {
			// 如果指针为nil，不添加条件
			if reflect.ValueOf(value).IsNil() {
				return qb
			}
			// 获取指针指向的值
			value = reflect.ValueOf(value).Elem().Interface()
		}
		// 添加 % 通配符
		likeValue := "%" + value.(string) + "%"
		qb.conditions = append(qb.conditions, Condition{
			Field:    field,
			Operator: OperatorLIKE,
			Value:    likeValue,
		})
	}
	return qb
}

// AddRangeCondition 添加范围查询条件
func (qb *QueryBuilder) AddRangeCondition(field string, start, end interface{}) *QueryBuilder {
	if start != nil || end != nil {
		qb.conditions = append(qb.conditions, Condition{
			Field:    field,
			Operator: OperatorBETWEEN,
			Value:    []interface{}{start, end},
		})
	}
	return qb
}

// AddSort 添加排序条件
func (qb *QueryBuilder) AddSort(field string, direction SortDirection) *QueryBuilder {
	fmt.Println("field", field, "direction", direction)
	if field != "" && direction != "" {
		qb.sorts = append(qb.sorts, Sort{
			Field:     field,
			Direction: direction,
		})
	}
	return qb
}

// AddGroup 添加分组条件
func (qb *QueryBuilder) AddGroup(field string) *QueryBuilder {
	qb.groups = append(qb.groups, field)
	return qb
}

// AddPreload 添加预加载
func (qb *QueryBuilder) AddPreload(relation string) *QueryBuilder {
	qb.preloads = append(qb.preloads, relation)
	return qb
}

// AddSelect 添加选择字段
func (qb *QueryBuilder) AddSelect(fields ...string) *QueryBuilder {
	qb.selects = append(qb.selects, fields...)
	return qb
}

// AddJoin 添加连接
func (qb *QueryBuilder) AddJoin(join string) *QueryBuilder {
	qb.joins = append(qb.joins, join)
	return qb
}

// SetPage 设置页码
func (qb *QueryBuilder) SetPage(page int) *QueryBuilder {
	qb.Pagination.SetPage(page)
	return qb
}

// SetPageSize 设置每页数量
func (qb *QueryBuilder) SetPageSize(pageSize int) *QueryBuilder {
	qb.Pagination.SetPageSize(pageSize)
	return qb
}

// SetPagination 设置分页参数
func (qb *QueryBuilder) SetPagination(page, pageSize int) *QueryBuilder {
	qb.SetPage(page)
	qb.SetPageSize(pageSize)
	return qb
}

// Build 构建查询
func (qb *QueryBuilder) Build(db *gorm.DB) *gorm.DB {
	// 添加选择字段
	if len(qb.selects) > 0 {
		db = db.Select(strings.Join(qb.selects, ", "))
	}

	// 添加连接
	for _, join := range qb.joins {
		db = db.Joins(join)
	}

	// 添加查询条件
	for _, cond := range qb.conditions {
		switch cond.Operator {
		case OperatorLIKE:
			db = db.Where(cond.Field+" LIKE ?", cond.Value)
		case OperatorIN:
			db = db.Where(cond.Field+" IN ?", cond.Value)
		case OperatorBETWEEN:
			values := cond.Value.([]interface{})
			if values[0] != nil && values[1] != nil {
				db = db.Where(cond.Field+" BETWEEN ? AND ?", values[0], values[1])
			} else if values[0] != nil {
				db = db.Where(cond.Field+" >= ?", values[0])
			} else if values[1] != nil {
				db = db.Where(cond.Field+" <= ?", values[1])
			}
		default:
			db = db.Where(cond.Field+" "+string(cond.Operator)+" ?", cond.Value)
		}
	}

	// 添加分组
	if len(qb.groups) > 0 {
		db = db.Group(strings.Join(qb.groups, ", "))
	}

	// 添加排序
	for _, sort := range qb.sorts {
		db = db.Order(sort.Field + " " + string(sort.Direction))
	}

	// 添加预加载
	for _, preload := range qb.preloads {
		db = db.Preload(preload)
	}

	return db
}

// ApplyPagination 应用分页
func (qb *QueryBuilder) ApplyPagination(db *gorm.DB) *gorm.DB {
	if qb.Pagination.Enabled {
		offset := (qb.Pagination.Page - 1) * qb.Pagination.PageSize
		db = db.Offset(offset).Limit(qb.Pagination.PageSize)
	}
	return db
}
