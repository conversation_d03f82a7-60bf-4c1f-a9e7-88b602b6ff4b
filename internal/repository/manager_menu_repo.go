package repository

import (
	"app/internal/model"
	"app/pkg/cache"
	"context"

	"gorm.io/gorm"
)

// ManagerMenuRepo 管理员菜单接口
type ManagerMenuRepo interface {
	// FindByID 按id查询
	FindByID(ctx context.Context, id uint) (*model.ManagerMenu, error)
	// FindListWithCount 按条件分页查询
	FindListWithCount(ctx context.Context, queryBuilder *QueryBuilder) ([]*model.ManagerMenu, int64, error)
	// Update 更新菜单
	Update(ctx context.Context, menu *model.ManagerMenu) error
}

// managerMenuRepo 管理员菜单实现
type managerMenuRepoImpl struct {
	db  *gorm.DB
	rds *cache.RedisService
}

// NewManagerMenuRepoImpl 创建管理员菜单实现
func NewManagerMenuRepoImpl(db *gorm.DB, rds *cache.RedisService) ManagerMenuRepo {
	return &managerMenuRepoImpl{
		db:  db,
		rds: rds,
	}
}

// FindByID 根据ID查询菜单
func (r *managerMenuRepoImpl) FindByID(ctx context.Context, id uint) (*model.ManagerMenu, error) {
	var menu model.ManagerMenu
	if err := r.db.WithContext(ctx).Where("id = ?", id).Find(&menu).Error; err != nil {
		return nil, err
	}
	if menu.ID == 0 {
		return nil, ErrRecordNotFound
	}
	return &menu, nil
}

// FindList 根据查询条件分页获取菜单列表
func (r *managerMenuRepoImpl) FindListWithCount(ctx context.Context, queryBuilder *QueryBuilder) ([]*model.ManagerMenu, int64, error) {
	var menus []*model.ManagerMenu
	var total int64

	db := r.db.WithContext(ctx).Model(&model.ManagerMenu{})
	db = queryBuilder.Build(db)
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	db = queryBuilder.ApplyPagination(db)
	if err := db.Find(&menus).Error; err != nil {
		return nil, 0, err
	}
	return menus, total, nil
}

// Update 修改菜单
func (r *managerMenuRepoImpl) Update(ctx context.Context, menu *model.ManagerMenu) error {
	return r.db.WithContext(ctx).Updates(menu).Error
}
