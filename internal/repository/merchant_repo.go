package repository

import (
	"app/internal/model"
	"app/pkg/cache"
	"context"

	"gorm.io/gorm"
)

// 商户接口
type MerchantRepo interface {
	// FindByID 查询商户
	FindByID(ctx context.Context, id uint) (*model.Merchant, error)
	// FindListCount 分页查询商户列表
	FindList(ctx context.Context, queryBuilder *QueryBuilder) ([]*model.Merchant, error)
	//  FindListWithCount 分页查询商户列表,获取总数
	FindListWithCount(ctx context.Context, queryBuilder *QueryBuilder) ([]*model.Merchant, int64, error)
	// Create 创建商户
	Create(ctx context.Context, manager *model.Merchant) error
	// Update 更新商户
	Update(ctx context.Context, manager *model.Merchant) error
	// Delete 软删除商户
	Delete(ctx context.Context, id uint) error
	// HardDelete 硬删除商户
	HardDelete(ctx context.Context, id uint) error
}

// ManagerRepoImpl 商户实现
type MerchantRepoImpl struct {
	db  *gorm.DB
	rds *cache.RedisService
}

// NewManagerRepoImpl 创建商户实现
func NewMerchantRepoImpl(db *gorm.DB, rds *cache.RedisService) MerchantRepo {
	return &MerchantRepoImpl{
		db:  db,
		rds: rds,
	}
}

// FindByID 根据ID查找商户
func (r *MerchantRepoImpl) FindByID(ctx context.Context, id uint) (*model.Merchant, error) {
	var merchant model.Merchant
	if err := r.db.WithContext(ctx).Where("id = ?", id).Find(&merchant).Error; err != nil {
		return nil, err
	}
	if merchant.ID == 0 {
		return nil, ErrRecordNotFound
	}
	return &merchant, nil
}

// FindList 查询商户列表
func (r *MerchantRepoImpl) FindList(ctx context.Context, queryBuilder *QueryBuilder) ([]*model.Merchant, error) {
	var logs []*model.Merchant
	db := r.db.WithContext(ctx).Model(&model.Merchant{})
	db = queryBuilder.Build(db)
	return logs, db.Find(&logs).Error
}

// FindListWithCount 分页查询商户列表
func (r *MerchantRepoImpl) FindListWithCount(ctx context.Context, queryBuilder *QueryBuilder) ([]*model.Merchant, int64, error) {
	var merchants []*model.Merchant
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Merchant{})
	// 构建查询条件
	db = queryBuilder.Build(db)

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	// 分页查询
	db = queryBuilder.ApplyPagination(db)
	if err := db.Find(&merchants).Error; err != nil {
		return nil, 0, err
	}
	return merchants, total, nil
}

// Create 创建商户
func (r *MerchantRepoImpl) Create(ctx context.Context, merchant *model.Merchant) error {
	return r.db.WithContext(ctx).Create(merchant).Error
}

// Update 更新商户
func (r *MerchantRepoImpl) Update(ctx context.Context, merchant *model.Merchant) error {
	return r.db.WithContext(ctx).Model(&model.Merchant{}).Where("id = ?", merchant.ID).Updates(merchant).Error
}

// // Delete 软删除商户
func (r *MerchantRepoImpl) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Merchant{}, id).Error
}

// HardDelete 硬删除商户
func (r *MerchantRepoImpl) HardDelete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Unscoped().Delete(&model.Merchant{}, id).Error
}
