# 数据流走向
Handler -> DTO -> Service -> Repository -> Model

# 文件命名命名规范
handler                     xxxx_handler.go
dto                         xxxx_dto.go
repository                  xxxx_repo.go
service                     xxxx_service.go

# 方法名命名规范

# dto
xxxCreateQuery               新增数据
xxxUpdateQuery               更新数据
xxxListQuery                 查询列表
xxxDeleteQuery               删除数据

# handler    NewXxxHandler
xxxCreate                   新增数据
xxxUpdate                   更新数据
xxxList                     查询列表
xxxDelete                   删除数据

# repository NewXxxRepo
FindByID                    查询单条数据使用ID
FindList                    查询列表, 使用构造器
FindListWithCount           查询列表, 返回总数
Create                      新增数据
Update                      更新数据
Delete                      删除数据
HardDelete                  硬删除数据