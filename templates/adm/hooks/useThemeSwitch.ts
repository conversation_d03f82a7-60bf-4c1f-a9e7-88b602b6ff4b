import { useTheme } from "next-themes";
import { useState, useEffect } from "react";

export function useThemeSwitch() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const switchTheme = () => {
    theme === "light" ? setTheme("dark") : setTheme("light");
  };

  // 如果组件未挂载，返回一个占位值
  if (!mounted) {
    return { theme: 'light', switchTheme };
  }

  return { theme, switchTheme };
}